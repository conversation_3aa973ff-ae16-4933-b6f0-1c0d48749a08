# Accounting App Workflows

## Overview
This accounting app is designed to help businesses identify discrepancies between their bank statements and internal ledger records through automated reconciliation and AI-powered analysis.

## Core Workflows

### 1. User Onboarding & Company Setup

**Flow:**
1. **User Registration/Login** → Supabase Auth
2. **Company Creation** → Automatic via `/api/ensure-company`
3. **Profile Setup** → User profile created automatically
4. **Dashboard Access** → User can access company dashboard

**Technical Details:**
- Uses Supabase authentication
- Automatically creates a company for each user
- Company slug generated from user's name/email
- Row Level Security (RLS) ensures data isolation

### 2. Document Upload & Processing

**Flow:**
1. **File Selection** → User drags/drops or selects files
2. **File Validation** → Check file type, size, format
3. **Upload to Storage** → Supabase Storage (`financial-documents` bucket)
4. **Database Record** → Create file record with metadata
5. **AI Processing** → Extract transaction data using AI APIs
6. **Transaction Storage** → Store extracted transactions in database

**Supported File Types:**
- **Bank Statements:** PDF, CSV, Excel (.xlsx, .xls)
- **Ledger Files:** CSV, Excel (.xlsx, .xls), JSON

**AI Processing:**
- **Bank Statements:** Mistral AI for PDF/text extraction
- **Ledger Files:** Google Gemini for structured data processing

### 3. Transaction Extraction & Categorization

**Bank Statement Processing:**
1. **PDF Text Extraction** → Extract raw text from PDF
2. **AI Analysis** → Mistral AI identifies transaction patterns
3. **Data Structuring** → Convert to standardized transaction format
4. **Validation** → Check for required fields (date, amount, description)
5. **Storage** → Save as `bank_statement` type transactions

**Ledger Processing:**
1. **File Parsing** → Parse CSV/Excel structure
2. **Column Mapping** → AI identifies relevant columns
3. **Data Cleaning** → Standardize formats, handle missing data
4. **Categorization** → Assign transaction categories
5. **Storage** → Save as `ledger_entry` type transactions

### 4. Automated Reconciliation

**Matching Algorithm:**
1. **Transaction Pairing** → Compare bank vs ledger transactions
2. **Similarity Scoring** → Calculate match confidence based on:
   - Amount matching (exact or within tolerance)
   - Date proximity (within configurable days)
   - Description similarity (fuzzy matching)
3. **Confidence Thresholds** → Auto-match high confidence pairs
4. **Manual Review Queue** → Flag uncertain matches for review

**Reconciliation Statuses:**
- `pending` → Awaiting processing
- `matched` → Automatically matched with high confidence
- `discrepancy` → Potential issues found
- `reviewed` → Manually reviewed by user

### 5. Discrepancy Detection & Reporting

**Types of Discrepancies:**
1. **Missing Transactions** → In bank but not in ledger (or vice versa)
2. **Amount Differences** → Same transaction, different amounts
3. **Date Mismatches** → Same transaction, different dates
4. **Duplicate Entries** → Multiple entries for same transaction

**Reporting Features:**
1. **Discrepancy Dashboard** → Visual overview of issues
2. **Detailed Reports** → Exportable reconciliation reports
3. **Trend Analysis** → Historical discrepancy patterns
4. **Action Items** → Prioritized list of items needing attention

### 6. Manual Review & Resolution

**Review Workflow:**
1. **Discrepancy List** → View all flagged items
2. **Side-by-Side Comparison** → Compare bank vs ledger entries
3. **Manual Matching** → User can manually link transactions
4. **Notes & Comments** → Add explanations for discrepancies
5. **Status Updates** → Mark items as resolved or escalated

**Resolution Actions:**
- **Accept Match** → Confirm AI suggestion
- **Reject Match** → Mark as false positive
- **Manual Link** → Connect different transactions
- **Mark as Exception** → Note legitimate discrepancy
- **Request Investigation** → Flag for further review

### 7. Audit Trail & Compliance

**Audit Logging:**
- All user actions are logged
- Changes to reconciliation status tracked
- File uploads and processing events recorded
- User access and permissions changes logged

**Compliance Features:**
- **Immutable Records** → Original data preserved
- **Change History** → Full audit trail of modifications
- **User Attribution** → Track who made what changes
- **Timestamp Tracking** → When actions occurred

## Technical Architecture

### Database Schema
```
accounting_companies → Company information
user_profiles → User details
company_users → User-company relationships
files → Uploaded documents
transactions → Extracted transaction data
reconciliations → Matching results
audit_logs → Audit trail
```

### API Endpoints
- `/api/ensure-company` → Company creation/retrieval
- `/api/process-document` → File upload and AI processing
- `/api/reconcile` → Run reconciliation algorithm
- `/api/reports` → Generate reconciliation reports

### External Integrations
- **Supabase** → Database, authentication, file storage
- **Mistral AI** → Bank statement text extraction
- **Google Gemini** → Ledger data processing
- **E2B** → Secure code execution environment

## Configuration & Settings

### Company Settings
- **Currency** → Default currency for transactions
- **Fiscal Year** → Company fiscal year start date
- **Timezone** → Company timezone for date handling
- **Reconciliation Rules** → Matching thresholds and tolerances

### Reconciliation Settings
- **Auto-match Threshold** → Confidence level for automatic matching (default: 95%)
- **Date Tolerance** → Days difference allowed for date matching (default: 3)
- **Amount Tolerance** → Cents difference allowed for amount matching (default: 0)

## Security & Privacy

### Data Protection
- **Row Level Security** → Users only see their company's data
- **Encrypted Storage** → Files encrypted at rest
- **Secure Processing** → AI processing in isolated environments
- **Access Controls** → Role-based permissions (admin, member, viewer)

### Compliance
- **SOX Compliance** → Audit trails for financial data
- **GDPR Ready** → User data privacy controls
- **Data Retention** → Configurable retention policies
- **Backup & Recovery** → Automated data backups

## Performance & Scalability

### Optimization
- **Database Indexing** → Optimized queries for large datasets
- **File Processing** → Asynchronous AI processing
- **Caching** → Query result caching for reports
- **Pagination** → Efficient data loading for large transaction sets

### Monitoring
- **Processing Status** → Real-time file processing updates
- **Error Handling** → Comprehensive error logging and recovery
- **Performance Metrics** → Processing time and accuracy tracking
- **Usage Analytics** → Company usage patterns and insights
