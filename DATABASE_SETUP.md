# Database Setup Instructions

## Prerequisites
1. You should have already created a Supabase project
2. You should have your API keys ready

## Step 1: Update Environment Variables
1. Open `.env.local` in your project root
2. Replace the placeholder values with your actual Supabase credentials:
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key_here
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
   ```

## Step 2: Run Database Migrations
There are two ways to set up your database schema:

### Option A: Using Supabase Dashboard (Recommended for now)
1. Go to your Supabase project dashboard
2. Navigate to the SQL Editor
3. Copy the entire contents of `supabase/migrations/20250917000001_initial_schema.sql`
4. Paste it into the SQL Editor and run it
5. Optionally, run the seed data from `supabase/seed.sql`

### Option B: Using Supabase CLI (Alternative)
If you have the Supabase CLI installed:
```bash
# Initialize Supabase in your project
supabase init

# Link to your remote project
supabase link --project-ref your-project-ref

# Apply the migration
supabase db push

# Apply seed data (optional)
supabase db reset --db-url "your-database-url"
```

## Step 3: Verify Database Setup
After running the migration, you should see these tables in your Supabase dashboard:
- `companies`
- `user_profiles`
- `company_users`
- `files`
- `transactions`
- `reconciliations`
- `audit_logs`

## Step 4: Create Your First User
1. Start the development server: `npm run dev`
2. Navigate to your app and sign up for an account
3. This will automatically create a user profile via the database trigger

## Step 5: Join the Demo Company (Optional)
After creating your user account:
1. Go to your Supabase dashboard
2. Navigate to the Table Editor → `company_users`
3. Insert a new row with:
   - `company_id`: `********-0000-0000-0000-************`
   - `user_id`: Your user ID (from `auth.users` table)
   - `role`: `admin`
   - `accepted_at`: Current timestamp

## Troubleshooting
- If you get RLS policy errors, make sure you're properly authenticated
- If migrations fail, check that you have the required extensions enabled
- For permission issues, verify your service role key is correct

## Next Steps
Once your database is set up:
1. Test authentication by signing up/in
2. Upload sample bank statements from the `example-docs/` folder
3. Begin testing the file processing workflow