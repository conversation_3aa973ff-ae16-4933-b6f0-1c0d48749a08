# Document Structure Analysis

## Bank Statement Structure (PDF - Commercial Bank of Ethiopia)

### Header Information
- **Bank**: Commercial Bank of Ethiopia
- **Branch**: Dechatu Branch
- **Account**: *************
- **Account Type**: CURRENT ACCOUNT
- **Period**: From 01 JUL 2025 to 31 JUL 2025
- **Account Name**: ECC SDCOH CRS SAFETY NET PROGRAM

### Transaction Table Structure
| Column | Field Name | Description | Example |
|--------|------------|-------------|---------|
| 1 | Date | Transaction Date | 03.07.2025 |
| 2 | Particulars | Transaction Type/Source | FIN 2001 2025, CHQ NO ******** |
| 3 | Reference | Reference Number | FT25184GZLQ1, TT25184TDLSD |
| 4 | Narrative | Transaction Description | FIN 2001 2025, ******** |
| 5 | Value Date | Value Date | 03.07.2025 |
| 6 | Debit | Debit Amount | -30,144.00 |
| 7 | Credit | Credit Amount | .00 |
| 8 | Balance | Running Balance | 5,155,083.47 |

### Sample Transactions
```
Date: 03.07.2025, Particulars: FIN 2001 2025, Reference: FT25184GZLQ1, Debit: -30,144.00, Balance: 5,155,083.47
Date: 03.07.2025, Particulars: FIN 2000 2025, Reference: FT25184625K1, Debit: -31,200.00, Balance: 5,123,883.47
Date: 03.07.2025, Particulars: CHQ NO ********, Reference: TT25184TDLSD, Debit: -40,000.00, Balance: 5,083,883.47
```

## Ledger Structure (Excel - General Ledger)

### Header Information
- **Organization**: ECC-SDC-HARARGHE CATHOLIC
- **Document Type**: General Ledger
- **Period**: From Jul 1, 2025 to Jul 31, 2025
- **Filter**: Report order is by ID

### Transaction Table Structure
| Column | Field Name | Description | Example |
|--------|------------|-------------|---------|
| A | Account ID | Account Identifier | 1010-0800-A3-00 |
| B | Account Description | Account Name | Bank CBE-... |
| C | Date | Transaction Date | 7/1/25 |
| D | Reference | Reference Number | FIN/1688/25, ********** |
| E | Journal | Journal Code | CDJ |
| F | Transaction Description | Description | PV 352714, Beginning... |
| G | Debit Amount | Debit Amount | [empty or amount] |
| H | Credit Amount | Credit Amount | 368,623.35 |
| I | Balance | Running Balance | 4,013.59 |

### Sample Transactions
```
Account: 1010-0800-A3-00, Date: 7/1/25, Reference: FIN/1688/25, Description: PV 352714, Credit: 368,623.35
Account: 1010-0800-A3-00, Date: 7/1/25, Reference: FI/1816/25, Description: PV-352826, Credit: 34,000.00
Account: 1010-0800-A3-00, Date: 7/1/25, Reference: **********, Description: PV-, Credit: 40,000.00
```

## Key Matching Fields

### Potential Matching Criteria
1. **Reference Numbers**:
   - Bank: TT25184TDLSD (CHQ NO ********)
   - Ledger: **********
   - **Match Pattern**: Both contain "********"

2. **Amounts**:
   - Bank: -40,000.00 (debit)
   - Ledger: 40,000.00 (credit)
   - **Match Pattern**: Same amount, opposite signs

3. **Dates**:
   - Bank: 03.07.2025
   - Ledger: 7/1/25
   - **Match Pattern**: Different formats but same date range

4. **Transaction Types**:
   - Bank: CHQ NO (Cheque Number)
   - Ledger: CD (Cash Disbursement)

## Data Extraction Requirements

### For Bank Statements (PDF):
- Extract date, particulars, reference, narrative, debit, credit, balance
- Handle date format: DD.MM.YYYY
- Handle amount format: -X,XXX.XX or .XX
- Clean reference numbers and narratives

### For Ledger Files (Excel):
- Extract account ID, date, reference, description, debit, credit, balance
- Handle date format: M/D/YY
- Handle amount format: X,XXX.XX
- Map journal codes (CDJ = Cash Disbursement Journal)

## Reconciliation Logic

### Primary Matching
1. **Exact Reference Match**: Extract numeric parts from references
2. **Amount Match**: Compare absolute values
3. **Date Range Match**: Within 1-3 day tolerance
4. **Transaction Type Correlation**: CHQ ↔ CD, FIN ↔ PV

### Secondary Matching (Fuzzy)
1. **Amount + Date Range**: For transactions without clear references
2. **Description Keywords**: Extract common identifiers
3. **Sequential Matching**: For bulk transactions

This structure will guide our AI model integration for processing these documents.