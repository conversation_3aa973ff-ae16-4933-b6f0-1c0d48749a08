# E2B Best Practices Guide for Accounting Applications

**Document**: E2B Implementation Guide
**Version**: 1.0
**Date**: September 17, 2025
**Purpose**: Secure file processing for financial applications

---

## 1. Overview

E2B provides secure, isolated cloud sandboxes (VMs) that are perfect for processing sensitive financial documents like bank statements and ledgers. This guide outlines best practices for implementing E2B in our accounting reconciliation application.

## 2. Why E2B for Financial Applications

### 2.1. Security Benefits
- **Isolated Execution**: Each file processing job runs in a separate VM (~150ms startup)
- **No Network Access**: Sandboxes have no internet access during processing
- **Resource Limits**: Memory, CPU, and time constraints prevent abuse
- **Automatic Cleanup**: VMs are destroyed after each job

### 2.2. Compliance Advantages
- **Data Isolation**: No data persistence between jobs
- **Audit Trails**: Complete logging of all operations
- **Controlled Environment**: Predictable, reproducible processing
- **Zero Trust**: No external dependencies during processing

---

## 3. Architecture Integration

### 3.1. Recommended Flow
```
Next.js API Route → Supabase Storage → E2B Processing → Postgres Storage
```

### 3.2. Implementation Pattern
```typescript
// Next.js API Route
export async function POST(request: Request) {
  // 1. Validate and store file
  const file = await request.formData()
  const { data: uploadData } = await supabase.storage
    .from('uploads')
    .upload(filename, file)

  // 2. Process in E2B sandbox
  const sandbox = await Sandbox.create()
  const results = await processFinancialData(sandbox, uploadData.path)

  // 3. Store results securely
  await supabase.from('reconciliation_results').insert(results)

  // 4. Cleanup
  await sandbox.close()
  await supabase.storage.from('uploads').remove([uploadData.path])
}
```

---

## 4. File Processing Best Practices

### 4.1. PDF Processing (Bank Statements) - Mistral AI Integration

**Recommended Approach: Mistral AI for PDF Document Extraction**

Mistral AI excels at parsing unstructured PDF documents, making it ideal for bank statements with complex layouts, tables, and varying formats.

```python
# E2B Sandbox Code with Mistral AI Integration
import requests
import json
import PyPDF2
import pdfplumber
import pandas as pd
from typing import List, Dict
import os

def extract_text_from_pdf(file_path: str) -> str:
    """Extract raw text from PDF for AI processing."""
    text_content = ""

    # Method 1: Use pdfplumber for better table extraction
    try:
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                text_content += page.extract_text() + "\n"
    except Exception as e:
        print(f"pdfplumber failed: {e}")

        # Fallback to PyPDF2
        try:
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                for page in pdf_reader.pages:
                    text_content += page.extract_text() + "\n"
        except Exception as e2:
            print(f"PyPDF2 also failed: {e2}")
            return ""

    return text_content

def process_bank_statement_with_mistral(file_path: str) -> List[Dict]:
    """Process PDF bank statement using Mistral AI for extraction."""

    # Extract raw text from PDF
    pdf_text = extract_text_from_pdf(file_path)

    if not pdf_text.strip():
        raise ValueError("Could not extract text from PDF")

    # Prepare Mistral AI prompt for transaction extraction
    prompt = f"""
    Extract banking transactions from the following bank statement text.
    Return a JSON array of transactions with the following structure:
    {{
        "date": "YYYY-MM-DD",
        "description": "transaction description",
        "reference": "reference number or code",
        "amount": decimal_number (positive for credits, negative for debits),
        "balance": decimal_number,
        "transaction_type": "credit" or "debit"
    }}

    Bank statement text:
    {pdf_text[:4000]}  # Limit to avoid token limits

    Return only valid JSON, no explanations:
    """

    # Call Mistral API
    mistral_response = call_mistral_api(prompt)

    # Parse and validate the response
    try:
        transactions = json.loads(mistral_response)
        return validate_and_clean_transactions(transactions)
    except json.JSONDecodeError as e:
        print(f"Failed to parse Mistral response: {e}")
        return fallback_pdf_processing(file_path)

def call_mistral_api(prompt: str) -> str:
    """Call Mistral API for document processing."""

    headers = {
        'Authorization': f'Bearer {os.getenv("MISTRAL_API_KEY")}',
        'Content-Type': 'application/json'
    }

    payload = {
        "model": "mistral-large-latest",
        "messages": [
            {
                "role": "system",
                "content": "You are a financial document processing expert. Extract transaction data accurately from bank statements."
            },
            {
                "role": "user",
                "content": prompt
            }
        ],
        "temperature": 0.1,  # Low temperature for consistent output
        "max_tokens": 2000
    }

    response = requests.post(
        'https://api.mistral.ai/v1/chat/completions',
        headers=headers,
        json=payload,
        timeout=30
    )

    if response.status_code == 200:
        return response.json()['choices'][0]['message']['content']
    else:
        raise Exception(f"Mistral API error: {response.status_code} - {response.text}")

def fallback_pdf_processing(file_path: str) -> List[Dict]:
    """Fallback to traditional PDF processing if AI fails."""

    transactions = []

    try:
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                # Extract tables
                tables = page.extract_tables()
                for table in tables:
                    if table and len(table) > 1:  # Has header and data
                        df = pd.DataFrame(table[1:], columns=table[0])

                        # Try to identify transaction columns
                        date_col = find_date_column(df.columns)
                        amount_col = find_amount_column(df.columns)
                        desc_col = find_description_column(df.columns)

                        if date_col and amount_col:
                            for _, row in df.iterrows():
                                transaction = extract_transaction_from_row(
                                    row, date_col, amount_col, desc_col
                                )
                                if transaction:
                                    transactions.append(transaction)

    except Exception as e:
        print(f"Fallback processing failed: {e}")

    return transactions
                if page_tables:
                    tables.extend(page_tables)

            if tables:
                return parse_tabular_data(tables)
    except Exception as e:
        log_error(f"Structured extraction failed: {e}")

    # Method 2: Fallback to OCR
    try:
        return extract_with_ocr(file_path)
    except Exception as e:
        log_error(f"OCR extraction failed: {e}")
        raise ProcessingError("Unable to extract data from PDF")

def parse_tabular_data(tables: List) -> List[Dict]:
    """Parse structured table data into transactions."""
    transactions = []

    for table in tables:
        df = pd.DataFrame(table[1:], columns=table[0])  # Skip header

        for _, row in df.iterrows():
            transaction = {
                'date': parse_date(row.get('Date', '')),
                'description': clean_description(row.get('Description', '')),
                'amount': parse_amount(row.get('Amount', '0')),
                'balance': parse_amount(row.get('Balance', '0')),
                'reference': extract_reference(row.get('Reference', ''))
            }

            if transaction['date']:  # Only include valid transactions
                transactions.append(transaction)

    return transactions
```

### 4.2. Excel Processing (Ledgers) - Gemini API Integration

**Recommended Approach: Google Gemini API for Excel Analysis**

Gemini API excels at understanding structured data formats, making it perfect for Excel ledger processing with intelligent column mapping and data validation.

```python
import openpyxl
import pandas as pd
import google.generativeai as genai
import json
import os
from decimal import Decimal
from typing import List, Dict, Any

def process_ledger_file_with_gemini(file_path: str) -> List[Dict]:
    """Process Excel/CSV ledger using Gemini API for intelligent parsing."""

    # Configure Gemini API
    genai.configure(api_key=os.getenv("GOOGLE_GENERATIVE_AI_API_KEY"))
    model = genai.GenerativeModel('gemini-1.5-pro')

    try:
        # Read the file into a DataFrame
        if file_path.endswith('.xlsx'):
            df = pd.read_excel(file_path, engine='openpyxl')
        elif file_path.endswith('.csv'):
            df = pd.read_csv(file_path)
        else:
            raise ValueError("Unsupported file format")

        # Convert DataFrame to string representation for AI analysis
        df_sample = df.head(10)  # Analyze first 10 rows for structure
        df_string = df_sample.to_string()

        # Create intelligent column mapping prompt
        mapping_prompt = f"""
        Analyze this Excel/CSV ledger data and map columns to standard accounting fields.

        Data sample:
        {df_string}

        Map the columns to these standard fields:
        - date: Transaction date
        - description: Transaction description/memo
        - amount: Transaction amount (positive/negative)
        - account: Account name/code
        - reference: Reference number
        - category: Transaction category
        - debit: Debit amount (if separate from amount)
        - credit: Credit amount (if separate from amount)

        Return JSON mapping like: {{"column_name": "standard_field"}}
        Only include columns that clearly match accounting data.

        Column mapping:
        """

        # Get column mapping from Gemini
        mapping_response = model.generate_content(mapping_prompt)
        column_mapping = parse_gemini_json_response(mapping_response.text)

        if not column_mapping:
            # Fallback to traditional processing
            return fallback_excel_processing(df)

        # Process each row using the mapping
        transactions = []
        for _, row in df.iterrows():
            transaction = process_row_with_mapping(row, column_mapping)
            if transaction:
                transactions.append(transaction)

        # Use Gemini for final validation and enhancement
        enhanced_transactions = enhance_transactions_with_gemini(
            transactions[:20], model  # Process first 20 for validation
        )

        return enhanced_transactions + transactions[20:]  # Rest without enhancement

    except Exception as e:
        print(f"Gemini processing failed: {e}")
        # Fallback to traditional processing
        return fallback_excel_processing(df)

def parse_gemini_json_response(response_text: str) -> Dict[str, str]:
    """Parse JSON response from Gemini, handling potential formatting issues."""

    try:
        # Clean the response (remove markdown, extra text)
        cleaned = response_text.strip()
        if '```json' in cleaned:
            cleaned = cleaned.split('```json')[1].split('```')[0]
        elif '```' in cleaned:
            cleaned = cleaned.split('```')[1].split('```')[0]

        return json.loads(cleaned)
    except (json.JSONDecodeError, IndexError) as e:
        print(f"Failed to parse Gemini response: {e}")
        return {}

def process_row_with_mapping(row: pd.Series, mapping: Dict[str, str]) -> Dict[str, Any]:
    """Process a single row using the column mapping."""

    transaction = {
        'date': None,
        'description': None,
        'amount': None,
        'account': None,
        'reference': None,
        'category': None,
        'transaction_type': 'ledger_entry'
    }

    for col_name, std_field in mapping.items():
        if col_name in row.index and std_field in transaction:
            value = row[col_name]

            # Special handling for different field types
            if std_field == 'date':
                transaction[std_field] = parse_date_value(value)
            elif std_field == 'amount':
                transaction[std_field] = parse_amount_value(value)
            else:
                transaction[std_field] = str(value) if pd.notna(value) else None

    # Validate required fields
    if transaction['date'] and transaction['amount'] is not None:
        return transaction
    return None

def enhance_transactions_with_gemini(transactions: List[Dict], model) -> List[Dict]:
    """Use Gemini to enhance and validate transaction data."""

    transactions_json = json.dumps(transactions, indent=2, default=str)

    enhancement_prompt = f"""
    Review and enhance these accounting transactions:

    {transactions_json}

    Please:
    1. Standardize date formats to YYYY-MM-DD
    2. Clean and standardize descriptions
    3. Validate amounts are numeric
    4. Categorize transactions if possible
    5. Flag any suspicious or invalid entries

    Return the enhanced JSON array:
    """

    try:
        response = model.generate_content(enhancement_prompt)
        enhanced = parse_gemini_json_response(response.text)

        if isinstance(enhanced, list):
            return enhanced
        else:
            return transactions

    except Exception as e:
        print(f"Enhancement failed: {e}")
        return transactions

def fallback_excel_processing(df: pd.DataFrame) -> List[Dict]:
    """Traditional Excel processing as fallback."""

    transactions = []

    # Try to identify columns automatically
    date_cols = [col for col in df.columns if 'date' in col.lower()]
    amount_cols = [col for col in df.columns if any(term in col.lower()
                  for term in ['amount', 'value', 'debit', 'credit'])]
    desc_cols = [col for col in df.columns if any(term in col.lower()
                for term in ['description', 'memo', 'detail', 'narration'])]

    if not (date_cols and amount_cols):
        raise ValueError("Could not identify required columns automatically")

    date_col = date_cols[0]
    amount_col = amount_cols[0]
    desc_col = desc_cols[0] if desc_cols else None

    for _, row in df.iterrows():
        try:
            transaction = {
                'date': parse_date_value(row[date_col]),
                'amount': parse_amount_value(row[amount_col]),
                'description': str(row[desc_col]) if desc_col and pd.notna(row[desc_col]) else None,
                'transaction_type': 'ledger_entry'
            }

            if transaction['date'] and transaction['amount'] is not None:
                transactions.append(transaction)

        except Exception as e:
            print(f"Error processing row: {e}")
            continue

    return transactions
        df.columns = df.columns.str.lower().str.strip()

        transactions = []
        for _, row in df.iterrows():
            transaction = {
                'date': validate_date(row['date']),
                'description': clean_text(row['description']),
                'debit': parse_decimal(row.get('debit', 0)),
                'credit': parse_decimal(row.get('credit', 0)),
                'amount': calculate_amount(row),
                'reference': row.get('reference', ''),
                'account': row.get('account', '')
            }

            # Validate transaction data
            if is_valid_transaction(transaction):
                transactions.append(transaction)

        return transactions

    except Exception as e:
        log_error(f"Ledger processing failed: {e}")
        raise ProcessingError(f"Unable to process ledger: {str(e)}")
```

### 4.3. Error Handling and Validation
```python
def validate_and_process_file(sandbox, file_info):
    """Main processing function with comprehensive error handling."""

    try:
        # 1. Download file to sandbox
        file_path = f"/tmp/{file_info['filename']}"
        sandbox.files.write(file_path, file_info['content'])

        # 2. Validate file integrity
        if not validate_file_integrity(file_path):
            raise SecurityError("File integrity validation failed")

        # 3. Determine processing method
        if file_path.endswith('.pdf'):
            results = process_bank_statement(file_path)
        elif file_path.endswith(('.xlsx', '.csv')):
            results = process_ledger_file(file_path)
        else:
            raise ValueError("Unsupported file type")

        # 4. Validate results
        validated_results = validate_extraction_results(results)

        # 5. Generate processing report
        report = generate_processing_report(validated_results, file_info)

        return {
            'success': True,
            'transactions': validated_results,
            'report': report,
            'metadata': extract_metadata(file_path)
        }

    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'error_type': type(e).__name__,
            'file_info': file_info['filename']
        }
    finally:
        # Always cleanup temporary files
        cleanup_temp_files()
```

---

## 5. Security Implementation

### 5.1. Sandbox Configuration
```typescript
// Recommended E2B sandbox configuration
const sandbox = await Sandbox.create({
  // Security settings
  timeout: 300, // 5 minutes max processing time
  memory: '2GB', // Sufficient for most files
  cpu: 2, // Adequate processing power

  // Environment isolation
  networkAccess: false, // No internet access
  persistentStorage: false, // No data persistence

  // Monitoring
  logging: true, // Enable audit logs
  metrics: true // Performance monitoring
})
```

### 5.2. Input Validation
```typescript
// Validate files before processing
function validateFileForProcessing(file: File): ValidationResult {
  const validations = [
    // Size limits
    { check: file.size <= 50 * 1024 * 1024, error: 'File too large (50MB limit)' },

    // Type validation
    {
      check: ['application/pdf', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'text/csv']
        .includes(file.type),
      error: 'Invalid file type'
    },

    // Name validation
    { check: /^[a-zA-Z0-9\-_\. ]+$/.test(file.name), error: 'Invalid filename characters' },

    // Content validation (basic)
    { check: file.size > 0, error: 'Empty file not allowed' }
  ]

  for (const validation of validations) {
    if (!validation.check) {
      return { valid: false, error: validation.error }
    }
  }

  return { valid: true }
}
```

### 5.3. Data Sanitization
```python
import re
from decimal import Decimal, InvalidOperation

def sanitize_financial_data(raw_data: str) -> str:
    """Sanitize financial data to prevent injection attacks."""

    # Remove potentially dangerous characters
    sanitized = re.sub(r'[<>{}();\'"\\]', '', raw_data)

    # Normalize whitespace
    sanitized = ' '.join(sanitized.split())

    # Limit length
    sanitized = sanitized[:1000]

    return sanitized.strip()

def parse_amount_safely(amount_str: str) -> Decimal:
    """Safely parse monetary amounts."""

    try:
        # Remove common currency symbols and formatting
        cleaned = re.sub(r'[$,£€¥\s]', '', str(amount_str))

        # Handle parentheses for negative amounts
        if cleaned.startswith('(') and cleaned.endswith(')'):
            cleaned = '-' + cleaned[1:-1]

        # Parse as decimal for precision
        return Decimal(cleaned)

    except (InvalidOperation, ValueError):
        return Decimal('0')
```

---

## 6. Performance Optimization

### 6.1. Batch Processing
```typescript
// Process multiple files efficiently
async function processFilesBatch(files: File[]) {
  const BATCH_SIZE = 3 // Optimal for E2B concurrency
  const results = []

  for (let i = 0; i < files.length; i += BATCH_SIZE) {
    const batch = files.slice(i, i + BATCH_SIZE)

    const batchPromises = batch.map(async (file) => {
      const sandbox = await Sandbox.create()
      try {
        return await processFileInSandbox(sandbox, file)
      } finally {
        await sandbox.close()
      }
    })

    const batchResults = await Promise.all(batchPromises)
    results.push(...batchResults)
  }

  return results
}
```

### 6.2. Progress Tracking
```typescript
// Real-time progress updates
export async function processWithProgress(
  files: File[],
  onProgress: (progress: ProcessingProgress) => void
) {
  const totalFiles = files.length
  let completed = 0

  for (const file of files) {
    onProgress({
      currentFile: file.name,
      completed,
      total: totalFiles,
      percentage: Math.round((completed / totalFiles) * 100),
      status: 'processing'
    })

    await processFile(file)
    completed++

    onProgress({
      currentFile: file.name,
      completed,
      total: totalFiles,
      percentage: Math.round((completed / totalFiles) * 100),
      status: completed === totalFiles ? 'complete' : 'processing'
    })
  }
}
```

---

## 7. Monitoring and Debugging

### 7.1. Comprehensive Logging
```python
import logging
import json
from datetime import datetime

# Configure structured logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def log_processing_event(event_type: str, data: dict):
    """Log processing events with structured data."""

    log_entry = {
        'timestamp': datetime.utcnow().isoformat(),
        'event_type': event_type,
        'sandbox_id': os.environ.get('E2B_SANDBOX_ID'),
        'user_id': data.get('user_id'),
        'file_name': data.get('file_name'),
        'file_size': data.get('file_size'),
        'processing_time': data.get('processing_time'),
        'success': data.get('success'),
        'error': data.get('error')
    }

    logging.info(json.dumps(log_entry))
```

### 7.2. Health Checks
```typescript
// Monitor E2B sandbox health
export async function healthCheck(): Promise<HealthStatus> {
  try {
    const sandbox = await Sandbox.create()
    const startTime = Date.now()

    // Test basic operations
    await sandbox.files.write('/tmp/test.txt', 'health check')
    const content = await sandbox.files.read('/tmp/test.txt')

    await sandbox.close()

    const responseTime = Date.now() - startTime

    return {
      status: 'healthy',
      responseTime,
      timestamp: new Date().toISOString()
    }

  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message,
      timestamp: new Date().toISOString()
    }
  }
}
```

---

## 8. Testing Strategies

### 8.1. Unit Testing
```typescript
// Test E2B integration
describe('E2B File Processing', () => {
  it('should process valid PDF bank statement', async () => {
    const mockPDF = await createMockBankStatement()
    const result = await processFileInSandbox(mockPDF)

    expect(result.success).toBe(true)
    expect(result.transactions).toHaveLength(10)
    expect(result.transactions[0]).toHaveProperty('date')
    expect(result.transactions[0]).toHaveProperty('amount')
  })

  it('should handle corrupted files gracefully', async () => {
    const corruptedFile = await createCorruptedFile()
    const result = await processFileInSandbox(corruptedFile)

    expect(result.success).toBe(false)
    expect(result.error).toContain('File integrity validation failed')
  })
})
```

### 8.2. Integration Testing
```typescript
// Test full workflow
describe('Full Processing Workflow', () => {
  it('should complete end-to-end processing', async () => {
    // 1. Upload file
    const uploadResult = await uploadFile(testBankStatement)
    expect(uploadResult.success).toBe(true)

    // 2. Process in E2B
    const processResult = await triggerProcessing(uploadResult.fileId)
    expect(processResult.status).toBe('completed')

    // 3. Verify results in database
    const dbResults = await getProcessingResults(uploadResult.fileId)
    expect(dbResults).toBeDefined()
    expect(dbResults.transactions.length).toBeGreaterThan(0)

    // 4. Verify cleanup
    const fileExists = await checkFileExists(uploadResult.fileId)
    expect(fileExists).toBe(false)
  })
})
```

---

## 9. Production Deployment

### 9.1. Environment Configuration
```typescript
// Production environment setup
const E2B_CONFIG = {
  production: {
    timeout: 600, // 10 minutes for large files
    memory: '4GB',
    cpu: 4,
    concurrency: 10, // Max concurrent sandboxes
    retries: 3,
    monitoring: true
  },

  development: {
    timeout: 300,
    memory: '2GB',
    cpu: 2,
    concurrency: 3,
    retries: 1,
    monitoring: false
  }
}
```

### 9.2. Error Recovery
```typescript
// Implement retry logic with exponential backoff
async function processWithRetry(
  file: File,
  maxRetries: number = 3
): Promise<ProcessingResult> {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await processFileInSandbox(file)
    } catch (error) {
      lastError = error

      if (attempt < maxRetries) {
        const delay = Math.pow(2, attempt) * 1000 // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay))
      }
    }
  }

  throw new Error(`Processing failed after ${maxRetries} attempts: ${lastError.message}`)
}
```

---

## 10. Cost Optimization

### 10.1. Resource Management
```typescript
// Optimize sandbox usage
class SandboxPool {
  private activeSandboxes = new Map<string, Sandbox>()
  private readonly maxConcurrent = 5

  async getAvailableSandbox(): Promise<Sandbox> {
    if (this.activeSandboxes.size >= this.maxConcurrent) {
      await this.waitForAvailableSlot()
    }

    const sandbox = await Sandbox.create()
    this.activeSandboxes.set(sandbox.id, sandbox)

    return sandbox
  }

  async releaseSandbox(sandboxId: string): Promise<void> {
    const sandbox = this.activeSandboxes.get(sandboxId)
    if (sandbox) {
      await sandbox.close()
      this.activeSandboxes.delete(sandboxId)
    }
  }
}
```

### 10.2. Usage Monitoring
```typescript
// Track usage for cost optimization
interface UsageMetrics {
  totalProcessingTime: number
  filesProcessed: number
  averageFileSize: number
  errorRate: number
  cost: number
}

function trackUsage(processingTime: number, fileSize: number, success: boolean) {
  const metrics = {
    processingTime,
    fileSize,
    success,
    timestamp: Date.now()
  }

  // Send to monitoring system
  sendMetrics('e2b_usage', metrics)
}
```

---

## 11. Summary

E2B provides the perfect solution for secure financial document processing with:

- **Security**: Isolated VMs with no data persistence
- **Performance**: Fast startup (~150ms) and efficient processing
- **Scalability**: Handle concurrent processing with resource limits
- **Compliance**: Complete audit trails and controlled environments

By following these best practices, our accounting application will have enterprise-grade security while maintaining high performance and reliability for financial data processing.

---

**Document Status**: Implementation Ready
**Review Date**: September 17, 2025
**Next Review**: October 17, 2025