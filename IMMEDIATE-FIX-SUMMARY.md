# 🚨 IMMEDIATE FIX REQUIRED - Database Schema Issue

## The Problem
Your accounting app is failing because it's trying to use database tables that don't exist. The error you're seeing:

```
Company creation error: {
  code: '23505',
  details: 'Key (slug)=(eshetudfeleke-s-company) already exists.',
  hint: null,
  message: 'duplicate key value violates unique constraint "companies_slug_key"'
}
```

This happens because:
1. Your database has a **loyalty program schema** (companies, receipts, loyalty_members, etc.)
2. Your accounting app expects a **different schema** (user_profiles, company_users, files, transactions, etc.)
3. The app is trying to use the wrong `companies` table

## 🔧 IMMEDIATE FIX (5 minutes)

### Step 1: Run Database Migration
1. Go to **Supabase Dashboard**: https://supabase.com/dashboard
2. Navigate to your project: `rrvdmpagsvhjdurfmqec`
3. Click **SQL Editor** in the left sidebar
4. Copy the entire contents of `simple-database-fix.sql` (in your project root)
5. Paste into the SQL Editor
6. Click **Run** button

**Note:** Use `simple-database-fix.sql` - it's a cleaner version that should run without errors.

### Step 2: Restart Your App
```bash
# Stop your development server (Ctrl+C)
# Then restart:
npm run dev
```

### Step 3: Test Upload
Try uploading documents again - the error should be gone!

## ✅ What This Fix Does

1. **Creates new tables** for the accounting app:
   - `accounting_companies` (separate from existing `companies`)
   - `user_profiles`, `company_users`, `files`, `transactions`, etc.

2. **Preserves existing data** - your loyalty program data stays intact

3. **Updates the API** to use the correct tables

4. **Sets up proper security** with Row Level Security policies

## 🎯 Expected Results After Fix

- ✅ File uploads work without errors
- ✅ Companies are created in the correct table
- ✅ Users can access their accounting dashboard
- ✅ AI processing of documents works
- ✅ Transaction reconciliation features available

## 📋 Files Created/Modified

### New Files:
- `fix-database-schema.sql` - Database migration script
- `README-DATABASE-FIX.md` - Detailed fix instructions
- `ACCOUNTING-APP-WORKFLOWS.md` - Complete workflow documentation
- `run-database-migration.js` - Alternative migration script

### Modified Files:
- `src/app/api/ensure-company/route.ts` - Updated to use correct tables

## 🔍 Verification Steps

After running the fix, verify it worked:

1. **Check Supabase Dashboard** → Table Editor should show new tables:
   - accounting_companies
   - user_profiles
   - company_users
   - files
   - transactions
   - reconciliations
   - audit_logs

2. **Test File Upload** → Upload a PDF or Excel file - should work without errors

3. **Check Browser Console** → Should see successful API calls instead of 500 errors

## 🚨 If Fix Doesn't Work

If you still get errors:

1. **Check SQL Editor output** for error messages
2. **Verify all tables were created** in Table Editor
3. **Clear browser cache** and restart dev server
4. **Check environment variables** in `.env.local`

## 📞 Need Help?

If you're still having issues after running the fix:
1. Share the SQL Editor error output
2. Check if all tables appear in your Supabase Table Editor
3. Verify your service role key has admin permissions

---

## 🎉 Next Steps After Fix

Once the database is working, you can:

1. **Upload Documents** → Bank statements (PDF) and ledgers (Excel/CSV)
2. **View Transactions** → See extracted transaction data
3. **Run Reconciliation** → Match bank vs ledger entries
4. **Review Discrepancies** → Identify and resolve mismatches
5. **Generate Reports** → Export reconciliation results

The accounting app provides automated bank reconciliation with AI-powered transaction extraction and discrepancy detection!
