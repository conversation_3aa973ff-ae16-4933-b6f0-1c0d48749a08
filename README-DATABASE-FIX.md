# Database Schema Fix for Accounting App

## Problem
Your accounting app is trying to use database tables that don't exist. The current database has a loyalty program schema, but the accounting app expects different tables.

## Solution

### Option 1: Quick Fix (Recommended)
1. Go to your Supabase dashboard: https://supabase.com/dashboard
2. Navigate to your project: `rrvdmpagsvhjdurfmqec`
3. Go to the **SQL Editor** tab
4. Copy the entire contents of `fix-database-schema.sql` 
5. Paste it into the SQL Editor
6. Click **Run** to execute the script

This will create the required tables (`accounting_companies`, `user_profiles`, `company_users`, `files`, `transactions`, `reconciliations`, `audit_logs`) without affecting your existing loyalty program data.

### Option 2: Command Line (if you have Supabase CLI access)
```bash
# Login to Supabase (you'll need to authenticate)
supabase login

# Link your project
supabase link --project-ref rrvdmpagsvhjdurfmqec

# Run the migration
supabase db push
```

## What the Fix Does

1. **Creates separate tables** for the accounting app (prefixed with `accounting_` where needed)
2. **Preserves existing data** - your loyalty program data remains untouched
3. **Sets up proper permissions** with Row Level Security (RLS)
4. **Creates necessary indexes** for performance
5. **Adds audit logging** for compliance

## Tables Created

- `accounting_companies` - Companies for the accounting app (separate from loyalty companies)
- `user_profiles` - User profile information
- `company_users` - Links users to companies with roles
- `files` - Uploaded financial documents
- `transactions` - Bank statements and ledger entries
- `reconciliations` - Matching results between bank and ledger
- `audit_logs` - Audit trail for all actions

## After Running the Fix

1. Restart your Next.js development server:
   ```bash
   npm run dev
   ```

2. Try uploading documents again - the error should be resolved

3. The app will now create companies in the `accounting_companies` table instead of conflicting with your existing `companies` table

## Verification

After running the SQL script, you can verify it worked by checking if these tables exist in your Supabase dashboard under **Table Editor**.

## Troubleshooting

If you still get errors after running the fix:

1. **Check the SQL Editor output** for any error messages
2. **Verify all tables were created** in the Table Editor
3. **Restart your development server** completely
4. **Clear your browser cache** and try again

If you continue having issues, the problem might be:
- Missing environment variables
- Incorrect Supabase service role permissions
- Network connectivity issues

## Next Steps

Once the database is fixed, you can proceed with using the accounting app features:
- Upload bank statements and ledgers
- View transaction reconciliation
- Generate discrepancy reports
