# Supabase Storage Setup Instructions

This document provides step-by-step instructions to set up Supabase Storage for the accounting app.

## 1. Create Storage Bucket

Run this in your Supabase SQL Editor or via the Dashboard:

```sql
-- Create the financial documents bucket
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'financial-documents',
  'financial-documents',
  false,
  ********, -- 10MB limit
  ARRAY[
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'text/csv'
  ]
);
```

## 2. Set Up Storage Policies

### Policy 1: Upload Files
```sql
CREATE POLICY "Company users can upload files" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'financial-documents' AND
    (storage.foldername(name))[1] = 'uploads' AND
    auth.uid() IN (
      SELECT user_id FROM company_users
      WHERE company_id::text = (storage.foldername(name))[2]
    )
  );
```

### Policy 2: View Files
```sql
CREATE POLICY "Company users can view files" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'financial-documents' AND
    auth.uid() IN (
      SELECT user_id FROM company_users
      WHERE company_id::text = (storage.foldername(name))[2]
    )
  );
```

### Policy 3: Delete Files (Admin Only)
```sql
CREATE POLICY "Company admins can delete files" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'financial-documents' AND
    auth.uid() IN (
      SELECT user_id FROM company_users
      WHERE company_id::text = (storage.foldername(name))[2]
      AND role = 'admin'
    )
  );
```

### Enable RLS
```sql
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;
```

## 3. Alternative: Use Supabase Dashboard

1. Go to your Supabase Dashboard
2. Navigate to **Storage** in the sidebar
3. Click **Create Bucket**
4. Name: `financial-documents`
5. Set as **Private** (not public)
6. Set file size limit to **10MB**
7. Add allowed MIME types:
   - `application/pdf`
   - `application/vnd.openxmlformats-officedocument.spreadsheetml.sheet`
   - `application/vnd.ms-excel`
   - `text/csv`

## 4. Environment Variables

Make sure your `.env.local` includes:

```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```

## 5. Test Upload

1. Start your development server: `npm run dev`
2. Navigate to `/dashboard/upload`
3. Try uploading a test PDF or Excel file
4. Check the Supabase Storage bucket to verify the file appears

## Security Notes

- Files are stored privately (not publicly accessible)
- Users can only access files from their own company
- Company admins can delete files
- All uploads are validated for file type and size
- Files are encrypted at rest by Supabase

## Troubleshooting

### Issue: "Bucket not found"
- Verify the bucket was created successfully
- Check the bucket name is exactly `financial-documents`

### Issue: "Permission denied"
- Ensure RLS policies are created and enabled
- Verify the user has a company association in `company_users` table

### Issue: "File type not allowed"
- Check the MIME type is in the allowed list
- Verify the file extension matches the MIME type

For more help, check the Supabase Storage documentation at https://supabase.com/docs/guides/storage