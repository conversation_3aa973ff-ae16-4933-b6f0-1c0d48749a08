-- Minimal Database Fix for Accounting App
-- This creates only the essential tables needed to fix the immediate error

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Step 1: Create accounting_companies table (separate from existing companies)
CREATE TABLE IF NOT EXISTS accounting_companies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 2: Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    full_name <PERSON><PERSON><PERSON><PERSON>(255),
    avatar_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 3: Create company_users table
CREATE TABLE IF NOT EXISTS company_users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES accounting_companies(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES user_profiles(id) ON DELETE CASCADE,
    role VARCHAR(50) NOT NULL DEFAULT 'member',
    invited_by UUID REFERENCES user_profiles(id),
    invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    accepted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(company_id, user_id)
);

-- Step 4: Create files table (without enum types for now)
CREATE TABLE IF NOT EXISTS files (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES accounting_companies(id) ON DELETE CASCADE,
    uploaded_by UUID NOT NULL REFERENCES user_profiles(id),
    filename VARCHAR(255) NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    storage_path TEXT NOT NULL,
    status VARCHAR(20) NOT NULL DEFAULT 'uploading', -- using VARCHAR instead of enum
    processing_started_at TIMESTAMP WITH TIME ZONE,
    processing_completed_at TIMESTAMP WITH TIME ZONE,
    processing_error TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 5: Create transactions table (without enum types for now)
CREATE TABLE IF NOT EXISTS transactions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    company_id UUID NOT NULL REFERENCES accounting_companies(id) ON DELETE CASCADE,
    file_id UUID REFERENCES files(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL, -- using VARCHAR instead of enum
    date DATE NOT NULL,
    description TEXT,
    amount DECIMAL(15,2) NOT NULL,
    reference VARCHAR(255),
    account VARCHAR(255),
    category VARCHAR(255),
    balance DECIMAL(15,2),
    raw_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Step 6: Enable Row Level Security
ALTER TABLE accounting_companies ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE company_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE files ENABLE ROW LEVEL SECURITY;
ALTER TABLE transactions ENABLE ROW LEVEL SECURITY;

-- Step 7: Create basic RLS policies
DO $$ 
BEGIN
    -- Drop existing policies if they exist
    DROP POLICY IF EXISTS "Users can view own profile" ON user_profiles;
    DROP POLICY IF EXISTS "Users can update own profile" ON user_profiles;
    DROP POLICY IF EXISTS "Users can create own profile" ON user_profiles;
    DROP POLICY IF EXISTS "Users can view their company memberships" ON company_users;
    DROP POLICY IF EXISTS "Users can join companies" ON company_users;
    DROP POLICY IF EXISTS "Users can view their companies" ON accounting_companies;
    DROP POLICY IF EXISTS "Users can create companies" ON accounting_companies;
    DROP POLICY IF EXISTS "Users can view company files" ON files;
    DROP POLICY IF EXISTS "Users can insert files to their companies" ON files;
    DROP POLICY IF EXISTS "Users can update files in their companies" ON files;
    
    -- Create new policies
    CREATE POLICY "Users can view own profile" ON user_profiles
        FOR SELECT USING (auth.uid() = id);

    CREATE POLICY "Users can update own profile" ON user_profiles
        FOR UPDATE USING (auth.uid() = id);

    CREATE POLICY "Users can create own profile" ON user_profiles
        FOR INSERT WITH CHECK (auth.uid() = id);

    CREATE POLICY "Users can view their company memberships" ON company_users
        FOR SELECT USING (auth.uid() = user_id);

    CREATE POLICY "Users can join companies" ON company_users
        FOR INSERT WITH CHECK (auth.uid() = user_id);

    CREATE POLICY "Users can view their companies" ON accounting_companies
        FOR SELECT USING (
            id IN (
                SELECT company_id FROM company_users
                WHERE user_id = auth.uid()
            )
        );

    CREATE POLICY "Users can create companies" ON accounting_companies
        FOR INSERT WITH CHECK (true);

    CREATE POLICY "Users can view company files" ON files
        FOR SELECT USING (
            company_id IN (
                SELECT company_id FROM company_users
                WHERE user_id = auth.uid()
            )
        );

    CREATE POLICY "Users can insert files to their companies" ON files
        FOR INSERT WITH CHECK (
            company_id IN (
                SELECT company_id FROM company_users
                WHERE user_id = auth.uid()
            )
            AND uploaded_by = auth.uid()
        );

    CREATE POLICY "Users can update files in their companies" ON files
        FOR UPDATE USING (
            company_id IN (
                SELECT company_id FROM company_users
                WHERE user_id = auth.uid()
            )
        );
END $$;

-- Step 8: Create trigger for user profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, full_name)
  VALUES (new.id, new.email, new.raw_user_meta_data->>'full_name')
  ON CONFLICT (id) DO NOTHING;
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop and recreate trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();

-- Step 9: Create basic indexes (only after tables are created)
CREATE INDEX IF NOT EXISTS idx_files_company_id ON files(company_id);
CREATE INDEX IF NOT EXISTS idx_files_uploaded_by ON files(uploaded_by);
CREATE INDEX IF NOT EXISTS idx_transactions_company_id ON transactions(company_id);
CREATE INDEX IF NOT EXISTS idx_transactions_file_id ON transactions(file_id);

-- Success message
DO $$ 
BEGIN
    RAISE NOTICE 'Database schema created successfully! You can now restart your app.';
END $$;
