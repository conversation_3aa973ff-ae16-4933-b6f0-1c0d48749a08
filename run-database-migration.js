#!/usr/bin/env node

/**
 * Database Migration Script for Accounting App
 * 
 * This script runs the database migration to create the required tables
 * for the accounting app while preserving existing loyalty program data.
 */

const { createClient } = require('@supabase/supabase-js')
const fs = require('fs')
const path = require('path')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:')
  console.error('   NEXT_PUBLIC_SUPABASE_URL')
  console.error('   SUPABASE_SERVICE_ROLE_KEY')
  console.error('\nPlease check your .env.local file.')
  process.exit(1)
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
})

async function runMigration() {
  try {
    console.log('🚀 Starting database migration for accounting app...')
    
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'fix-database-schema.sql')
    
    if (!fs.existsSync(migrationPath)) {
      console.error('❌ Migration file not found:', migrationPath)
      process.exit(1)
    }
    
    const migrationSQL = fs.readFileSync(migrationPath, 'utf8')
    
    console.log('📄 Running migration SQL...')
    
    // Execute the migration
    const { data, error } = await supabase.rpc('exec_sql', {
      sql: migrationSQL
    })
    
    if (error) {
      // If the RPC function doesn't exist, try direct SQL execution
      console.log('⚠️  RPC function not available, trying direct execution...')
      
      // Split the SQL into individual statements and execute them
      const statements = migrationSQL
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'))
      
      for (const statement of statements) {
        if (statement.trim()) {
          console.log('Executing:', statement.substring(0, 50) + '...')
          const { error: stmtError } = await supabase.rpc('exec_sql', {
            sql: statement + ';'
          })
          
          if (stmtError) {
            console.error('❌ Error executing statement:', stmtError.message)
            console.error('Statement:', statement.substring(0, 100) + '...')
            // Continue with other statements
          }
        }
      }
    }
    
    console.log('✅ Migration completed successfully!')
    
    // Verify that the tables were created
    console.log('🔍 Verifying table creation...')
    
    const tablesToCheck = [
      'accounting_companies',
      'user_profiles', 
      'company_users',
      'files',
      'transactions',
      'reconciliations',
      'audit_logs'
    ]
    
    for (const tableName of tablesToCheck) {
      const { data: tableExists, error: checkError } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
      
      if (checkError) {
        console.log(`❌ Table '${tableName}' not found or not accessible`)
      } else {
        console.log(`✅ Table '${tableName}' exists and is accessible`)
      }
    }
    
    console.log('\n🎉 Database migration completed!')
    console.log('You can now use the accounting app features.')
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    console.error('\nTroubleshooting:')
    console.error('1. Make sure your Supabase service role key has admin permissions')
    console.error('2. Check that your database connection is working')
    console.error('3. You may need to run the SQL manually in the Supabase dashboard')
    process.exit(1)
  }
}

// Alternative function to provide manual instructions
function showManualInstructions() {
  console.log('\n📋 MANUAL MIGRATION INSTRUCTIONS:')
  console.log('If the automatic migration fails, follow these steps:')
  console.log('')
  console.log('1. Go to your Supabase dashboard: https://supabase.com/dashboard')
  console.log('2. Navigate to your project: rrvdmpagsvhjdurfmqec')
  console.log('3. Go to the SQL Editor')
  console.log('4. Copy and paste the contents of fix-database-schema.sql')
  console.log('5. Run the SQL script')
  console.log('')
  console.log('This will create the required tables for the accounting app.')
}

// Run the migration
runMigration().catch(error => {
  console.error('❌ Unexpected error:', error)
  showManualInstructions()
  process.exit(1)
})
