'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import Link from 'next/link'
import {
  FileText,
  Upload,
  CheckSquare,
  AlertTriangle,
  TrendingUp,
  Calendar,
  DollarSign,
  ArrowUpRight,
  Clock,
  Users,
  PieChart,
  Activity
} from 'lucide-react'

export default function DashboardPage() {
  const [stats, setStats] = useState({
    totalFiles: 0,
    pendingReconciliations: 0,
    totalDiscrepancies: 0,
    totalAmount: 0
  })

  // This would be replaced with actual data fetching from Supabase
  useEffect(() => {
    // Mock data for now
    setStats({
      totalFiles: 12,
      pendingReconciliations: 8,
      totalDiscrepancies: 3,
      totalAmount: 125420.50
    })
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Dashboard</h1>
          <p className="mt-2 text-gray-600">
            Welcome back! Here's an overview of your reconciliation status.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <Link href="/dashboard/upload">
            <Button>
              <Upload className="mr-2 h-4 w-4" />
              Upload Files
            </Button>
          </Link>
          <Link href="/dashboard/reconciliation">
            <Button variant="outline">
              <CheckSquare className="mr-2 h-4 w-4" />
              View Reconciliations
            </Button>
          </Link>
        </div>
      </div>

      {/* Stats cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-blue-500/10 to-blue-600/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Files</CardTitle>
            <div className="p-2 bg-blue-100 rounded-lg">
              <FileText className="h-4 w-4 text-blue-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{stats.totalFiles}</div>
            <div className="flex items-center mt-1">
              <p className="text-xs text-gray-500">Bank statements and ledgers</p>
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-amber-500/10 to-amber-600/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Pending Reconciliations</CardTitle>
            <div className="p-2 bg-amber-100 rounded-lg">
              <Clock className="h-4 w-4 text-amber-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{stats.pendingReconciliations}</div>
            <div className="flex items-center mt-1">
              <p className="text-xs text-gray-500">Awaiting review</p>
              {stats.pendingReconciliations > 0 && (
                <Badge variant="secondary" className="ml-2 text-xs">Urgent</Badge>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-red-500/10 to-red-600/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Active Discrepancies</CardTitle>
            <div className="p-2 bg-red-100 rounded-lg">
              <AlertTriangle className="h-4 w-4 text-red-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{stats.totalDiscrepancies}</div>
            <div className="flex items-center mt-1">
              <p className="text-xs text-gray-500">Require attention</p>
              {stats.totalDiscrepancies > 0 && (
                <Badge variant="destructive" className="ml-2 text-xs">Action needed</Badge>
              )}
            </div>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-green-500/10 to-green-600/10 rounded-bl-full"></div>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">Total Amount</CardTitle>
            <div className="p-2 bg-green-100 rounded-lg">
              <DollarSign className="h-4 w-4 text-green-600" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">{formatCurrency(stats.totalAmount)}</div>
            <div className="flex items-center mt-1">
              <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
              <p className="text-xs text-gray-500">Processed this month</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick actions and insights */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <Card className="lg:col-span-2">
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center">
                  <Activity className="mr-2 h-5 w-5 text-blue-600" />
                  Quick Actions
                </CardTitle>
                <CardDescription>
                  Common tasks to get your reconciliation work done
                </CardDescription>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-4 border border-gray-200 rounded-lg hover:border-blue-300 hover:bg-blue-50/50 transition-colors group">
                <div className="flex items-start space-x-3">
                  <div className="p-2 bg-blue-100 rounded-lg group-hover:bg-blue-200">
                    <Upload className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">Upload Documents</h3>
                    <p className="text-sm text-gray-600 mt-1">Upload bank statements and ledger files</p>
                    <Link href="/dashboard/upload">
                      <Button size="sm" className="mt-3">
                        Start Upload
                        <ArrowUpRight className="ml-1 h-3 w-3" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg hover:border-green-300 hover:bg-green-50/50 transition-colors group">
                <div className="flex items-start space-x-3">
                  <div className="p-2 bg-green-100 rounded-lg group-hover:bg-green-200">
                    <CheckSquare className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">Review Reconciliations</h3>
                    <p className="text-sm text-gray-600 mt-1">{stats.pendingReconciliations} items need review</p>
                    <Link href="/dashboard/reconciliation">
                      <Button variant="outline" size="sm" className="mt-3">
                        Review Items
                        <ArrowUpRight className="ml-1 h-3 w-3" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg hover:border-purple-300 hover:bg-purple-50/50 transition-colors group">
                <div className="flex items-start space-x-3">
                  <div className="p-2 bg-purple-100 rounded-lg group-hover:bg-purple-200">
                    <PieChart className="h-5 w-5 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">Generate Reports</h3>
                    <p className="text-sm text-gray-600 mt-1">Create reconciliation reports</p>
                    <Link href="/dashboard/reports">
                      <Button variant="outline" size="sm" className="mt-3">
                        View Reports
                        <ArrowUpRight className="ml-1 h-3 w-3" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>

              <div className="p-4 border border-gray-200 rounded-lg hover:border-amber-300 hover:bg-amber-50/50 transition-colors group">
                <div className="flex items-start space-x-3">
                  <div className="p-2 bg-amber-100 rounded-lg group-hover:bg-amber-200">
                    <FileText className="h-5 w-5 text-amber-600" />
                  </div>
                  <div className="flex-1">
                    <h3 className="font-medium text-gray-900">Manage Files</h3>
                    <p className="text-sm text-gray-600 mt-1">View and organize uploaded files</p>
                    <Link href="/dashboard/files">
                      <Button variant="outline" size="sm" className="mt-3">
                        Browse Files
                        <ArrowUpRight className="ml-1 h-3 w-3" />
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5 text-gray-600" />
              Recent Activity
            </CardTitle>
            <CardDescription>
              Latest reconciliation activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <div className="h-2 w-2 bg-green-500 rounded-full"></div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">Bank statement processed</p>
                  <p className="text-xs text-gray-500 truncate">RFSA_July_2025_CBE_bank_statement.xlsx</p>
                  <p className="text-xs text-gray-400">2 hours ago</p>
                </div>
              </div>

              <Separator />

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <div className="h-2 w-2 bg-yellow-500 rounded-full"></div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">3 discrepancies found</p>
                  <p className="text-xs text-gray-500">July 2025 reconciliation</p>
                  <p className="text-xs text-gray-400">4 hours ago</p>
                </div>
              </div>

              <Separator />

              <div className="flex items-start space-x-3">
                <div className="flex-shrink-0 mt-1">
                  <div className="h-2 w-2 bg-blue-500 rounded-full"></div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900">New file uploaded</p>
                  <p className="text-xs text-gray-500 truncate">RFSA bank statement July 2025.pdf</p>
                  <p className="text-xs text-gray-400">6 hours ago</p>
                </div>
              </div>
            </div>

            <div className="mt-6">
              <Link href="/dashboard/files">
                <Button variant="ghost" size="sm" className="w-full">
                  View all activity
                  <ArrowUpRight className="ml-1 h-3 w-3" />
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Getting started */}
      {stats.totalFiles === 0 && (
        <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50">
          <CardHeader>
            <CardTitle className="text-blue-900 flex items-center">
              <TrendingUp className="mr-2 h-5 w-5" />
              Getting Started
            </CardTitle>
            <CardDescription className="text-blue-700">
              Welcome to FinanceSync! Here's how to get started with reconciliation:
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-start space-x-3 p-3 bg-white rounded-lg border border-blue-100">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                    1
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Upload Files</h3>
                    <p className="text-sm text-gray-600 mt-1">Upload your bank statements and ledger files</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 p-3 bg-white rounded-lg border border-blue-100">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                    2
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Auto Processing</h3>
                    <p className="text-sm text-gray-600 mt-1">Our AI automatically processes and matches transactions</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 p-3 bg-white rounded-lg border border-blue-100">
                  <div className="flex-shrink-0 w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                    3
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-900">Review & Report</h3>
                    <p className="text-sm text-gray-600 mt-1">Review discrepancies and generate reports</p>
                  </div>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row gap-3 pt-2">
                <Link href="/dashboard/upload">
                  <Button className="flex-1 sm:flex-none">
                    <Upload className="mr-2 h-4 w-4" />
                    Start by uploading files
                  </Button>
                </Link>
                <Button variant="outline" className="flex-1 sm:flex-none">
                  <FileText className="mr-2 h-4 w-4" />
                  View documentation
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}