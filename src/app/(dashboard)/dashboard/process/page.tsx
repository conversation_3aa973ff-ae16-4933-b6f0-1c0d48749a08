'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { FileUploadZone } from '@/components/upload/file-upload-zone'
import { Progress } from '@/components/ui/progress'
import { AlertCircle, CheckCircle, FileText, Settings, Upload as UploadIcon } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useCallback } from 'react'

interface ProcessingStatus {
  file_id: string
  name: string
  status: 'uploading' | 'processing' | 'completed' | 'failed'
  processing_result?: any
  created_at: string
  updated_at: string
}

export default function ProcessDocumentPage() {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])
  const [mistral<PERSON><PERSON><PERSON><PERSON>, setMistralApi<PERSON><PERSON>] = useState('')
  const [geminiApiKey, setGeminiApiKey] = useState('')
  const [processing, setProcessing] = useState(false)
  const [processingStatuses, setProcessingStatuses] = useState<Map<string, ProcessingStatus>>(new Map())
  const [error, setError] = useState('')

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const fileList = e.target.files
    if (fileList) {
      const newFiles = Array.from(fileList)
      setSelectedFiles(prev => [...prev, ...newFiles])
      setError('')
    }
  }, [])

  const removeFile = (fileName: string) => {
    setSelectedFiles(prev => prev.filter(f => f.name !== fileName))
  }

  const handleProcessDocument = async (file: File, fileType: 'bank_statement' | 'ledger') => {
    if (!file) return

    // Validate API keys
    if (fileType === 'bank_statement' && !mistralApiKey) {
      setError('Mistral API key is required for bank statement processing')
      return
    }

    if (fileType === 'ledger' && !geminiApiKey) {
      setError('Gemini API key is required for ledger processing')
      return
    }

    setProcessing(true)
    setError('')

    try {
      // Create form data
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', fileType)

      if (fileType === 'bank_statement') {
        formData.append('mistral_key', mistralApiKey)
      } else {
        formData.append('gemini_key', geminiApiKey)
      }

      // Process document
      const response = await fetch('/api/process-document', {
        method: 'POST',
        body: formData,
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Processing failed')
      }

      if (result.success) {
        // Update status
        setProcessingStatuses(prev => new Map(prev).set(file.name, {
          file_id: result.data.file_id,
          name: file.name,
          status: 'completed',
          processing_result: result.data.processing_result,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }))

        // Remove processed file from selection
        setSelectedFiles(prev => prev.filter(f => f.name !== file.name))
      } else {
        throw new Error(result.error || 'Processing failed')
      }

    } catch (error) {
      console.error('Processing error:', error)
      setError(error instanceof Error ? error.message : 'Processing failed')

      // Update status to failed
      setProcessingStatuses(prev => new Map(prev).set(file.name, {
        file_id: '',
        name: file.name,
        status: 'failed',
        processing_result: { error: error instanceof Error ? error.message : 'Processing failed' },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }))
    } finally {
      setProcessing(false)
    }
  }

  const getFileTypeFromName = (fileName: string): 'bank_statement' | 'ledger' => {
    const lower = fileName.toLowerCase()
    if (lower.includes('bank') || lower.includes('statement') || lower.endsWith('.pdf')) {
      return 'bank_statement'
    }
    return 'ledger'
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center space-x-2">
        <FileText className="w-6 h-6" />
        <h1 className="text-2xl font-bold">Document Processing</h1>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Processing Area */}
        <div className="lg:col-span-2 space-y-6">
          {/* File Upload */}
          <Card>
            <CardHeader>
              <CardTitle>Upload Documents</CardTitle>
              <CardDescription>
                Upload bank statements (PDF) or ledger files (Excel/CSV) for AI-powered processing
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center">
                <UploadIcon className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                <div className="space-y-2">
                  <p className="text-lg font-medium">Choose files to process</p>
                  <p className="text-sm text-gray-500">
                    Upload bank statements (PDF) or ledger files (Excel/CSV)
                  </p>
                </div>
                <input
                  type="file"
                  id="file-input"
                  className="hidden"
                  multiple
                  accept=".pdf,.xlsx,.xls,.csv"
                  onChange={handleFileInputChange}
                />
                <Button
                  onClick={() => document.getElementById('file-input')?.click()}
                  className="mt-4"
                >
                  Select Files
                </Button>
              </div>

              {selectedFiles.length > 0 && (
                <div className="space-y-2">
                  <h4 className="font-medium">Selected Files:</h4>
                  {selectedFiles.map((file) => (
                    <div key={file.name} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm">{file.name}</span>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.name)}
                      >
                        ×
                      </Button>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Selected Files */}
          {selectedFiles.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Ready for Processing</CardTitle>
                <CardDescription>
                  Files ready to be processed with AI extraction
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {selectedFiles.map((file) => {
                  const fileType = getFileTypeFromName(file.name)
                  return (
                    <div key={file.name} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <FileText className="w-5 h-5 text-muted-foreground" />
                        <div>
                          <p className="font-medium">{file.name}</p>
                          <p className="text-sm text-muted-foreground">
                            {fileType === 'bank_statement' ? 'Bank Statement (PDF)' : 'Ledger File (Excel/CSV)'}
                            • {(file.size / 1024 / 1024).toFixed(2)} MB
                          </p>
                        </div>
                      </div>
                      <Button
                        onClick={() => handleProcessDocument(file, fileType)}
                        disabled={processing}
                        size="sm"
                      >
                        {processing ? 'Processing...' : 'Process'}
                      </Button>
                    </div>
                  )
                })}
              </CardContent>
            </Card>
          )}

          {/* Processing Results */}
          {processingStatuses.size > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Processing Results</CardTitle>
                <CardDescription>
                  Status and results of document processing
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                {Array.from(processingStatuses.entries()).map(([fileName, status]) => (
                  <div key={fileName} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {status.status === 'completed' && <CheckCircle className="w-5 h-5 text-green-500" />}
                      {status.status === 'failed' && <AlertCircle className="w-5 h-5 text-red-500" />}
                      {status.status === 'processing' && <Progress value={50} className="w-5 h-5" />}

                      <div>
                        <p className="font-medium">{status.name}</p>
                        <p className="text-sm text-muted-foreground capitalize">
                          Status: {status.status}
                          {status.processing_result?.data?.transactions &&
                            ` • ${status.processing_result.data.transactions.length} transactions extracted`
                          }
                        </p>
                      </div>
                    </div>

                    {status.status === 'completed' && status.processing_result?.data && (
                      <Button variant="outline" size="sm">
                        View Details
                      </Button>
                    )}
                  </div>
                ))}
              </CardContent>
            </Card>
          )}

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>

        {/* API Configuration Sidebar */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="w-5 h-5" />
                <span>AI Configuration</span>
              </CardTitle>
              <CardDescription>
                Configure API keys for document processing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Tabs defaultValue="mistral" className="space-y-4">
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="mistral">Mistral AI</TabsTrigger>
                  <TabsTrigger value="gemini">Gemini AI</TabsTrigger>
                </TabsList>

                <TabsContent value="mistral" className="space-y-4">
                  <div>
                    <Label htmlFor="mistral-key">Mistral API Key</Label>
                    <Input
                      id="mistral-key"
                      type="password"
                      placeholder="Enter Mistral API key"
                      value={mistralApiKey}
                      onChange={(e) => setMistralApiKey(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Used for processing bank statements (PDF files)
                    </p>
                  </div>
                </TabsContent>

                <TabsContent value="gemini" className="space-y-4">
                  <div>
                    <Label htmlFor="gemini-key">Gemini API Key</Label>
                    <Input
                      id="gemini-key"
                      type="password"
                      placeholder="Enter Gemini API key"
                      value={geminiApiKey}
                      onChange={(e) => setGeminiApiKey(e.target.value)}
                    />
                    <p className="text-xs text-muted-foreground mt-1">
                      Used for processing ledger files (Excel/CSV)
                    </p>
                  </div>
                </TabsContent>
              </Tabs>
            </CardContent>
          </Card>

          {/* Processing Info */}
          <Card>
            <CardHeader>
              <CardTitle>How It Works</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5 flex-shrink-0" />
                <p>Upload your financial documents (PDF bank statements or Excel/CSV ledgers)</p>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5 flex-shrink-0" />
                <p>AI extracts transaction data with high accuracy using OCR and pattern recognition</p>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5 flex-shrink-0" />
                <p>Structured data is saved to your account for reconciliation analysis</p>
              </div>
              <div className="flex items-start space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full mt-1.5 flex-shrink-0" />
                <p>All processing happens in secure, isolated cloud environments</p>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}