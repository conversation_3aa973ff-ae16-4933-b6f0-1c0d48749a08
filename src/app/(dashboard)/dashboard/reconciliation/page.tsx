'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { 
  CheckSquare, 
  AlertTriangle, 
  FileText, 
  DollarSign, 
  TrendingUp,
  Search,
  Filter,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  ArrowRight
} from 'lucide-react'
import Link from 'next/link'

interface Transaction {
  id: string
  date: string
  description: string
  amount: number
  reference?: string
  source: 'bank' | 'ledger'
  matched: boolean
  matchedWith?: string
  confidence?: number
}

interface ReconciliationSummary {
  totalBankTransactions: number
  totalLedgerTransactions: number
  matchedTransactions: number
  unmatchedBankTransactions: number
  unmatchedLedgerTransactions: number
  totalDiscrepancy: number
  reconciliationStatus: 'balanced' | 'discrepancies' | 'pending'
}

export default function ReconciliationPage() {
  const [summary, setSummary] = useState<ReconciliationSummary>({
    totalBankTransactions: 0,
    totalLedgerTransactions: 0,
    matchedTransactions: 0,
    unmatchedBankTransactions: 0,
    unmatchedLedgerTransactions: 0,
    totalDiscrepancy: 0,
    reconciliationStatus: 'pending'
  })

  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState<'all' | 'matched' | 'unmatched'>('all')
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Mock data - replace with actual API calls
    setTimeout(() => {
      setSummary({
        totalBankTransactions: 45,
        totalLedgerTransactions: 42,
        matchedTransactions: 38,
        unmatchedBankTransactions: 7,
        unmatchedLedgerTransactions: 4,
        totalDiscrepancy: 1250.75,
        reconciliationStatus: 'discrepancies'
      })

      setTransactions([
        {
          id: '1',
          date: '2025-01-15',
          description: 'Office Supplies Purchase',
          amount: -245.50,
          reference: 'REF001',
          source: 'bank',
          matched: true,
          matchedWith: 'ledger-001',
          confidence: 98
        },
        {
          id: '2',
          date: '2025-01-14',
          description: 'Client Payment - ABC Corp',
          amount: 5000.00,
          reference: 'INV-2025-001',
          source: 'bank',
          matched: true,
          matchedWith: 'ledger-002',
          confidence: 100
        },
        {
          id: '3',
          date: '2025-01-13',
          description: 'Unmatched Bank Fee',
          amount: -25.00,
          source: 'bank',
          matched: false
        },
        {
          id: '4',
          date: '2025-01-12',
          description: 'Petty Cash Expense',
          amount: -50.00,
          reference: 'PC-001',
          source: 'ledger',
          matched: false
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch = transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         transaction.reference?.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterStatus === 'all' || 
                         (filterStatus === 'matched' && transaction.matched) ||
                         (filterStatus === 'unmatched' && !transaction.matched)
    
    return matchesSearch && matchesFilter
  })

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-sm text-gray-600">Loading reconciliation data...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Reconciliation</h1>
          <p className="mt-2 text-gray-600">
            Review and manage transaction matching between bank statements and ledger entries
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex space-x-3">
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Report
          </Button>
          <Link href="/dashboard/upload">
            <Button>
              <FileText className="mr-2 h-4 w-4" />
              Upload More Files
            </Button>
          </Link>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {summary.totalBankTransactions + summary.totalLedgerTransactions}
            </div>
            <p className="text-xs text-muted-foreground">
              {summary.totalBankTransactions} bank • {summary.totalLedgerTransactions} ledger
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Matched</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summary.matchedTransactions}</div>
            <p className="text-xs text-muted-foreground">
              {Math.round((summary.matchedTransactions / summary.totalBankTransactions) * 100)}% match rate
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unmatched</CardTitle>
            <XCircle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {summary.unmatchedBankTransactions + summary.unmatchedLedgerTransactions}
            </div>
            <p className="text-xs text-muted-foreground">
              {summary.unmatchedBankTransactions} bank • {summary.unmatchedLedgerTransactions} ledger
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Discrepancy</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">
              {formatCurrency(Math.abs(summary.totalDiscrepancy))}
            </div>
            <p className="text-xs text-muted-foreground">
              Requires investigation
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Status Alert */}
      {summary.reconciliationStatus === 'discrepancies' && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Reconciliation incomplete. {summary.unmatchedBankTransactions + summary.unmatchedLedgerTransactions} unmatched transactions found with total discrepancy of {formatCurrency(Math.abs(summary.totalDiscrepancy))}.
          </AlertDescription>
        </Alert>
      )}

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>Transaction Details</CardTitle>
          <CardDescription>
            Review individual transactions and their matching status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search transactions..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <Tabs value={filterStatus} onValueChange={(value) => setFilterStatus(value as any)}>
              <TabsList>
                <TabsTrigger value="all">All</TabsTrigger>
                <TabsTrigger value="matched">Matched</TabsTrigger>
                <TabsTrigger value="unmatched">Unmatched</TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="space-y-4">
            {filteredTransactions.map((transaction) => (
              <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {transaction.matched ? (
                      <CheckCircle className="h-5 w-5 text-green-600" />
                    ) : (
                      <XCircle className="h-5 w-5 text-red-600" />
                    )}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="font-medium text-gray-900">{transaction.description}</p>
                      <Badge variant={transaction.source === 'bank' ? 'default' : 'secondary'}>
                        {transaction.source}
                      </Badge>
                      {transaction.matched && transaction.confidence && (
                        <Badge variant="outline">
                          {transaction.confidence}% match
                        </Badge>
                      )}
                    </div>
                    <div className="flex items-center space-x-4 mt-1">
                      <p className="text-sm text-gray-500">{transaction.date}</p>
                      {transaction.reference && (
                        <p className="text-sm text-gray-500">Ref: {transaction.reference}</p>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className={`font-medium ${transaction.amount >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(transaction.amount)}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                  {!transaction.matched && (
                    <Button variant="outline" size="sm">
                      Match Manually
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {filteredTransactions.length === 0 && (
            <div className="text-center py-8">
              <p className="text-gray-500">No transactions found matching your criteria.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Next Steps */}
      {summary.reconciliationStatus === 'discrepancies' && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-900">Next Steps</CardTitle>
            <CardDescription className="text-blue-700">
              Recommended actions to complete your reconciliation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                  1
                </div>
                <p className="text-blue-900">Review unmatched transactions and attempt manual matching</p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                  2
                </div>
                <p className="text-blue-900">Generate journal voucher entries for remaining discrepancies</p>
              </div>
              <div className="flex items-center space-x-3">
                <div className="flex-shrink-0 w-6 h-6 bg-blue-600 text-white rounded-full flex items-center justify-center text-sm font-semibold">
                  3
                </div>
                <p className="text-blue-900">Export reconciliation report for your records</p>
              </div>
            </div>
            <div className="flex space-x-3 mt-6">
              <Link href="/dashboard/reports">
                <Button>
                  Generate Report
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Button>
              </Link>
              <Button variant="outline">
                Manual Review
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
