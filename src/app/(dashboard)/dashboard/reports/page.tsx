'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Calendar } from '@/components/ui/calendar'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { 
  BarChart3, 
  Download, 
  FileText, 
  Calendar as CalendarIcon,
  TrendingUp,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Eye,
  Filter,
  RefreshCw
} from 'lucide-react'
import { format } from 'date-fns'
import Link from 'next/link'

interface ReportData {
  id: string
  name: string
  type: 'reconciliation' | 'discrepancy' | 'journal_voucher' | 'summary'
  dateRange: string
  status: 'completed' | 'pending' | 'failed'
  createdAt: string
  fileSize?: string
  downloadUrl?: string
}

interface ReportSummary {
  totalReports: number
  completedReports: number
  totalDiscrepancies: number
  totalAmount: number
  lastGenerated: string
}

export default function ReportsPage() {
  const [reports, setReports] = useState<ReportData[]>([])
  const [summary, setSummary] = useState<ReportSummary>({
    totalReports: 0,
    completedReports: 0,
    totalDiscrepancies: 0,
    totalAmount: 0,
    lastGenerated: ''
  })
  
  const [selectedReportType, setSelectedReportType] = useState<string>('reconciliation')
  const [dateFrom, setDateFrom] = useState<Date>()
  const [dateTo, setDateTo] = useState<Date>()
  const [loading, setLoading] = useState(true)
  const [generating, setGenerating] = useState(false)

  useEffect(() => {
    // Mock data - replace with actual API calls
    setTimeout(() => {
      setSummary({
        totalReports: 12,
        completedReports: 10,
        totalDiscrepancies: 8,
        totalAmount: 15420.75,
        lastGenerated: '2025-01-15T10:30:00Z'
      })

      setReports([
        {
          id: '1',
          name: 'January 2025 Reconciliation Report',
          type: 'reconciliation',
          dateRange: 'Jan 1 - Jan 15, 2025',
          status: 'completed',
          createdAt: '2025-01-15T10:30:00Z',
          fileSize: '2.4 MB',
          downloadUrl: '/reports/jan-2025-reconciliation.pdf'
        },
        {
          id: '2',
          name: 'Discrepancy Analysis - Week 2',
          type: 'discrepancy',
          dateRange: 'Jan 8 - Jan 14, 2025',
          status: 'completed',
          createdAt: '2025-01-14T16:45:00Z',
          fileSize: '1.8 MB',
          downloadUrl: '/reports/week2-discrepancies.pdf'
        },
        {
          id: '3',
          name: 'Journal Voucher Recommendations',
          type: 'journal_voucher',
          dateRange: 'Jan 1 - Jan 15, 2025',
          status: 'pending',
          createdAt: '2025-01-15T11:00:00Z'
        },
        {
          id: '4',
          name: 'Monthly Summary Report',
          type: 'summary',
          dateRange: 'December 2024',
          status: 'completed',
          createdAt: '2025-01-01T09:00:00Z',
          fileSize: '3.2 MB',
          downloadUrl: '/reports/dec-2024-summary.pdf'
        }
      ])
      setLoading(false)
    }, 1000)
  }, [])

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return format(new Date(dateString), 'MMM dd, yyyy HH:mm')
  }

  const handleGenerateReport = async () => {
    setGenerating(true)
    // Mock report generation
    setTimeout(() => {
      const newReport: ReportData = {
        id: Date.now().toString(),
        name: `${selectedReportType.charAt(0).toUpperCase() + selectedReportType.slice(1)} Report`,
        type: selectedReportType as any,
        dateRange: dateFrom && dateTo ? `${format(dateFrom, 'MMM dd')} - ${format(dateTo, 'MMM dd, yyyy')}` : 'Custom Range',
        status: 'pending',
        createdAt: new Date().toISOString()
      }
      setReports(prev => [newReport, ...prev])
      setGenerating(false)
    }, 2000)
  }

  const getReportTypeColor = (type: string) => {
    switch (type) {
      case 'reconciliation': return 'bg-blue-100 text-blue-800'
      case 'discrepancy': return 'bg-red-100 text-red-800'
      case 'journal_voucher': return 'bg-yellow-100 text-yellow-800'
      case 'summary': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'pending': return <RefreshCw className="h-4 w-4 text-yellow-600 animate-spin" />
      case 'failed': return <AlertTriangle className="h-4 w-4 text-red-600" />
      default: return null
    }
  }

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-sm text-gray-600">Loading reports...</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Reports</h1>
          <p className="mt-2 text-gray-600">
            Generate and download reconciliation reports, discrepancy analyses, and journal voucher recommendations
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link href="/dashboard/reconciliation">
            <Button variant="outline">
              <BarChart3 className="mr-2 h-4 w-4" />
              View Reconciliation
            </Button>
          </Link>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Reports</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalReports}</div>
            <p className="text-xs text-muted-foreground">
              {summary.completedReports} completed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Discrepancies</CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{summary.totalDiscrepancies}</div>
            <p className="text-xs text-muted-foreground">
              Requiring attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(summary.totalAmount)}</div>
            <p className="text-xs text-muted-foreground">
              In discrepancies
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Last Generated</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">Today</div>
            <p className="text-xs text-muted-foreground">
              {formatDate(summary.lastGenerated)}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Report Generation */}
      <Card>
        <CardHeader>
          <CardTitle>Generate New Report</CardTitle>
          <CardDescription>
            Create custom reports for your reconciliation data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="space-y-2">
              <Label htmlFor="report-type">Report Type</Label>
              <Select value={selectedReportType} onValueChange={setSelectedReportType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="reconciliation">Reconciliation Summary</SelectItem>
                  <SelectItem value="discrepancy">Discrepancy Analysis</SelectItem>
                  <SelectItem value="journal_voucher">Journal Voucher Recommendations</SelectItem>
                  <SelectItem value="summary">Monthly Summary</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Date From</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateFrom ? format(dateFrom, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dateFrom}
                    onSelect={setDateFrom}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label>Date To</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full justify-start text-left font-normal">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateTo ? format(dateTo, "PPP") : "Pick a date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar
                    mode="single"
                    selected={dateTo}
                    onSelect={setDateTo}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label>&nbsp;</Label>
              <Button 
                onClick={handleGenerateReport} 
                disabled={generating}
                className="w-full"
              >
                {generating ? (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <FileText className="mr-2 h-4 w-4" />
                    Generate Report
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Reports List */}
      <Card>
        <CardHeader>
          <CardTitle>Generated Reports</CardTitle>
          <CardDescription>
            Download and manage your reconciliation reports
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {reports.map((report) => (
              <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <div className="flex-shrink-0">
                    {getStatusIcon(report.status)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <p className="font-medium text-gray-900">{report.name}</p>
                      <Badge className={getReportTypeColor(report.type)}>
                        {report.type.replace('_', ' ')}
                      </Badge>
                    </div>
                    <div className="flex items-center space-x-4 mt-1">
                      <p className="text-sm text-gray-500">{report.dateRange}</p>
                      <p className="text-sm text-gray-500">Generated: {formatDate(report.createdAt)}</p>
                      {report.fileSize && (
                        <p className="text-sm text-gray-500">{report.fileSize}</p>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <Button variant="ghost" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                  {report.status === 'completed' && report.downloadUrl && (
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>

          {reports.length === 0 && (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No reports generated yet.</p>
              <p className="text-sm text-gray-400 mt-1">Generate your first report using the form above.</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
