import { FileUploadZone } from '@/components/upload/file-upload-zone'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import Link from 'next/link'
import {
  FileText,
  Upload,
  CheckCircle,
  Clock,
  AlertTriangle,
  ArrowRight,
  Zap,
  Shield,
  FileSearch,
  Building2,
  Receipt
} from 'lucide-react'

export default function UploadPage() {
  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">Upload Documents</h1>
          <p className="mt-2 text-gray-600">
            Upload your bank statements and ledger files to start the reconciliation process
          </p>
        </div>
        <div className="mt-4 sm:mt-0">
          <Link href="/dashboard/process">
            <Button variant="outline">
              <FileSearch className="mr-2 h-4 w-4" />
              View Processing Queue
            </Button>
          </Link>
        </div>
      </div>

      {/* Upload Tabs */}
      <Tabs defaultValue="all" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all">All Documents</TabsTrigger>
          <TabsTrigger value="bank" className="flex items-center gap-2">
            <Building2 className="h-4 w-4" />
            Bank Statements
          </TabsTrigger>
          <TabsTrigger value="ledger" className="flex items-center gap-2">
            <Receipt className="h-4 w-4" />
            Ledger Files
          </TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Upload className="mr-2 h-5 w-5 text-gray-600" />
                Upload All Document Types
              </CardTitle>
              <CardDescription>
                Upload both bank statements and ledger files together
              </CardDescription>
            </CardHeader>
            <CardContent>
              <FileUploadZone />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bank" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center">
                    <Building2 className="mr-2 h-5 w-5 text-blue-600" />
                    Bank Statements
                  </CardTitle>
                  <CardDescription>
                    Upload PDF bank statements from your financial institution
                  </CardDescription>
                </div>
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  PDF Only
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">Bank Statement Requirements</h3>
                <ul className="space-y-1 text-sm text-blue-800">
                  <li>• PDF format only (.pdf)</li>
                  <li>• Must contain transaction details (dates, amounts, descriptions)</li>
                  <li>• Monthly or quarterly statements preferred</li>
                  <li>• Ensure text is readable (not just scanned images)</li>
                </ul>
              </div>
              <FileUploadZone documentType="bank_statement" />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="ledger" className="mt-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center">
                    <Receipt className="mr-2 h-5 w-5 text-green-600" />
                    Ledger Files
                  </CardTitle>
                  <CardDescription>
                    Upload Excel or CSV files containing your accounting ledger data
                  </CardDescription>
                </div>
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  Excel/CSV
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <h3 className="font-medium text-green-900 mb-2">Ledger File Requirements</h3>
                <ul className="space-y-1 text-sm text-green-800">
                  <li>• Excel (.xlsx, .xls) or CSV (.csv) format</li>
                  <li>• Include columns: Date, Amount, Description, Account</li>
                  <li>• First row should contain column headers</li>
                  <li>• Ensure data is in tabular format</li>
                </ul>
              </div>
              <FileUploadZone documentType="ledger" />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Features and Benefits */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-blue-500/10 to-blue-600/10 rounded-bl-full"></div>
          <CardHeader>
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Zap className="h-5 w-5 text-blue-600" />
              </div>
              <CardTitle className="text-lg">Instant Processing</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Files are processed automatically using AI-powered document analysis for immediate results.
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-green-500/10 to-green-600/10 rounded-bl-full"></div>
          <CardHeader>
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Shield className="h-5 w-5 text-green-600" />
              </div>
              <CardTitle className="text-lg">Secure & Compliant</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              All uploads are encrypted and processed in secure sandboxes with automatic deletion.
            </p>
          </CardContent>
        </Card>

        <Card className="relative overflow-hidden">
          <div className="absolute top-0 right-0 w-16 h-16 bg-gradient-to-br from-purple-500/10 to-purple-600/10 rounded-bl-full"></div>
          <CardHeader>
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <CheckCircle className="h-5 w-5 text-purple-600" />
              </div>
              <CardTitle className="text-lg">Smart Matching</CardTitle>
            </div>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600">
              Advanced algorithms automatically match transactions and identify discrepancies.
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Instructions */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5 text-gray-600" />
            Upload Guidelines
          </CardTitle>
          <CardDescription>
            Follow these best practices for optimal reconciliation results
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
                  Bank Statements
                </Badge>
                <span className="text-sm text-gray-500">PDF Format</span>
              </div>
              <ul className="space-y-3 text-sm text-gray-600">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Upload PDF files directly from your bank</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Ensure statements contain transaction details with dates, amounts, and descriptions</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Monthly or quarterly statements work best</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Multiple months can be uploaded separately for processing</span>
                </li>
              </ul>
            </div>

            <div>
              <div className="flex items-center space-x-2 mb-4">
                <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                  Ledger Files
                </Badge>
                <span className="text-sm text-gray-500">Excel/CSV Format</span>
              </div>
              <ul className="space-y-3 text-sm text-gray-600">
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Excel (.xlsx, .xls) or CSV files are supported</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Include columns for date, amount, description, and account</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>Ensure data is in tabular format with proper headers</span>
                </li>
                <li className="flex items-start space-x-3">
                  <CheckCircle className="h-4 w-4 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>First row should contain column headers for best results</span>
                </li>
              </ul>
            </div>
          </div>

          <Separator className="my-6" />

          <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-amber-600 flex-shrink-0 mt-0.5" />
              <div>
                <h3 className="font-medium text-amber-900 mb-2">Important Notes</h3>
                <ul className="space-y-1 text-sm text-amber-800">
                  <li>• Files are automatically deleted after processing for security</li>
                  <li>• Processing typically takes 2-5 minutes depending on file size</li>
                  <li>• You'll receive notifications when processing is complete</li>
                  <li>• Ensure file formats match the supported types for best results</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="mt-6 flex flex-col sm:flex-row gap-3">
            <Link href="/dashboard/process" className="flex-1">
              <Button variant="outline" className="w-full">
                <Clock className="mr-2 h-4 w-4" />
                View Processing Status
              </Button>
            </Link>
            <Link href="/dashboard/files" className="flex-1">
              <Button variant="outline" className="w-full">
                <FileText className="mr-2 h-4 w-4" />
                Manage Uploaded Files
              </Button>
            </Link>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}