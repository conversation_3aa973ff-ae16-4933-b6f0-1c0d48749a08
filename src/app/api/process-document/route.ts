import { NextRequest, NextResponse } from 'next/server'
import { DocumentProcessor } from '@/lib/document-processor'
import { createServerClient } from '@/lib/supabase-server'

export async function POST(request: NextRequest) {
  try {
    const supabase = createServerClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    // Parse form data
    const formData = await request.formData()
    const file = formData.get('file') as File
    const fileType = formData.get('type') as string // 'bank_statement' or 'ledger'
    const mistralApiKey = formData.get('mistral_key') as string
    const geminiApiKey = formData.get('gemini_key') as string

    if (!file) {
      return NextResponse.json(
        { error: 'No file provided' },
        { status: 400 }
      )
    }

    if (!fileType || !['bank_statement', 'ledger'].includes(fileType)) {
      return NextResponse.json(
        { error: 'Invalid file type. Must be bank_statement or ledger' },
        { status: 400 }
      )
    }

    // Validate API keys based on file type
    if (fileType === 'bank_statement' && !mistralApiKey) {
      return NextResponse.json(
        { error: 'Mistral API key required for bank statement processing' },
        { status: 400 }
      )
    }

    if (fileType === 'ledger' && !geminiApiKey) {
      return NextResponse.json(
        { error: 'Gemini API key required for ledger processing' },
        { status: 400 }
      )
    }

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Check if file record exists (should be created by upload process)
    const { data: fileRecord } = await supabase
      .from('files')
      .select('*')
      .eq('filename', file.name)
      .eq('company_id', companyUser.company_id)
      .eq('status', 'completed') // Files are completed upload, ready for processing
      .single()

    if (!fileRecord) {
      return NextResponse.json(
        { error: 'File not found or not ready for processing' },
        { status: 404 }
      )
    }

    // Update file status to processing
    await supabase
      .from('files')
      .update({
        status: 'processing',
        updated_at: new Date().toISOString()
      })
      .eq('id', fileRecord.id)

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    // Initialize document processor
    const processor = new DocumentProcessor()

    try {
      await processor.createSandbox()

      let result
      if (fileType === 'bank_statement') {
        result = await processor.processBankStatementPDF(buffer, file.name, mistralApiKey)
      } else {
        result = await processor.processLedgerFile(buffer, file.name, geminiApiKey)
      }

      if (result.success && result.data) {
        // Store extracted transactions
        const transactions = result.data.transactions || []

        if (transactions.length > 0) {
          // Insert transactions into database
          const transactionsToInsert = transactions.map((transaction: any) => ({
            file_id: fileRecord.id,
            company_id: companyUser.company_id,
            date: transaction.date,
            reference: transaction.reference || transaction.journal_code || '',
            description: transaction.particulars || transaction.description || transaction.narrative || '',
            amount: transaction.debit || transaction.credit || transaction.amount || 0,
            balance: transaction.balance || null,
            account: transaction.account || null,
            category: transaction.category || null,
            transaction_type: fileType,
            raw_data: transaction
          }))

          const { error: insertError } = await supabase
            .from('transactions')
            .insert(transactionsToInsert)

          if (insertError) {
            console.error('Error inserting transactions:', insertError)
            throw new Error('Failed to save transactions')
          }
        }

        // Update file status to completed
        await supabase
          .from('files')
          .update({
            status: 'completed',
            metadata: {
              ...(fileRecord.metadata as Record<string, any> || {}),
              processing_result: result as any
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', fileRecord.id)

        return NextResponse.json({
          success: true,
          message: 'File processed successfully',
          data: {
            transactions_count: transactions.length,
            file_id: fileRecord.id,
            processing_result: result
          }
        })

      } else {
        // Update file status to failed
        await supabase
          .from('files')
          .update({
            status: 'failed',
            metadata: {
              ...(fileRecord.metadata as Record<string, any> || {}),
              processing_result: result as any
            },
            updated_at: new Date().toISOString()
          })
          .eq('id', fileRecord.id)

        return NextResponse.json({
          success: false,
          error: result.error || 'Processing failed',
          file_id: fileRecord.id
        }, { status: 500 })
      }

    } finally {
      await processor.cleanup()
    }

  } catch (error) {
    console.error('Processing error:', error)
    return NextResponse.json(
      { error: 'Internal server error during processing' },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  try {
    const supabase = createServerClient()

    // Check authentication
    const { data: { user }, error: authError } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const fileId = searchParams.get('file_id')

    if (!fileId) {
      return NextResponse.json(
        { error: 'File ID required' },
        { status: 400 }
      )
    }

    // Get company ID for the user
    const { data: companyUser } = await supabase
      .from('company_users')
      .select('company_id')
      .eq('user_id', user.id)
      .single()

    if (!companyUser) {
      return NextResponse.json(
        { error: 'User not associated with any company' },
        { status: 403 }
      )
    }

    // Get processing status
    const { data: fileRecord, error } = await supabase
      .from('files')
      .select('*')
      .eq('id', fileId)
      .eq('company_id', companyUser.company_id)
      .single()

    if (error || !fileRecord) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      )
    }

    return NextResponse.json({
      file_id: fileRecord.id,
      name: fileRecord.filename,
      status: fileRecord.status,
      processing_result: (fileRecord.metadata as any)?.processing_result,
      created_at: fileRecord.created_at,
      updated_at: fileRecord.updated_at
    })

  } catch (error) {
    console.error('Status check error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}