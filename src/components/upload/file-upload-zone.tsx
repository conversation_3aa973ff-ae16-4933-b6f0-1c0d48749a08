'use client'

import { useState, useCallback, useRef } from 'react'
import { useDropzone } from 'react-dropzone'
import { useMutation, useQueryClient } from '@tanstack/react-query'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Progress } from '@/components/ui/progress'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Upload,
  FileText,
  File,
  X,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react'
import { createClient } from '@/lib/supabase'

interface FileUpload {
  id: string
  file: File
  progress: number
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed'
  error?: string
  supabaseFileId?: string
}

interface FileUploadZoneProps {
  documentType?: 'bank_statement' | 'ledger' | 'all'
}

const ACCEPTED_FILE_TYPES = {
  'application/pdf': ['.pdf'],
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'application/vnd.ms-excel': ['.xls'],
  'text/csv': ['.csv'],
}

const BANK_STATEMENT_TYPES = {
  'application/pdf': ['.pdf'],
}

const LEDGER_TYPES = {
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
  'application/vnd.ms-excel': ['.xls'],
  'text/csv': ['.csv'],
}

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

export function FileUploadZone({ documentType = 'all' }: FileUploadZoneProps) {
  const [files, setFiles] = useState<FileUpload[]>([])
  const [isDragActive, setIsDragActive] = useState(false)
  const queryClient = useQueryClient()
  const supabase = createClient()

  // Helper function to ensure user has a company
  const ensureUserCompany = async () => {
    // Get the current session token
    const { data: { session } } = await supabase.auth.getSession()
    if (!session) {
      throw new Error('No active session')
    }

    // Call the API endpoint to ensure company exists
    const response = await fetch('/api/ensure-company', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error || 'Failed to ensure company')
    }

    const { companyId } = await response.json()
    return companyId
  }

  const uploadMutation = useMutation({
    mutationFn: async (fileUpload: FileUpload) => {
      const { file } = fileUpload

      // Get authenticated user
      const { data: { user } } = await supabase.auth.getUser()
      if (!user) {
        throw new Error('User not authenticated')
      }

      // Ensure user has a company (create one if needed)
      const companyId = await ensureUserCompany()

      // Create a unique file name with timestamp
      const fileExt = file.name.split('.').pop()
      const fileName = `${Date.now()}-${Math.random().toString(36).substring(2)}.${fileExt}`
      // Simplified path using user ID for initial MVP
      const filePath = `uploads/${user.id}/${fileName}`      // Upload to Supabase Storage
      const { data, error } = await supabase.storage
        .from('financial-documents')
        .upload(filePath, file, {
          cacheControl: '3600',
          upsert: false
        })

      if (error) {
        throw new Error(`Upload failed: ${error.message}`)
      }

      // Create file record in database
      const { data: fileRecord, error: dbError } = await supabase
        .from('files')
        .insert({
          company_id: companyId,
          uploaded_by: user.id,
          filename: fileName,
          original_filename: file.name,
          file_size: file.size,
          mime_type: file.type,
          storage_path: data.path,
          status: 'uploading',
          metadata: {
            upload_timestamp: new Date().toISOString(),
            file_type: documentType !== 'all' ? documentType : (file.type.includes('pdf') ? 'bank_statement' : 'ledger')
          }
        })
        .select('id')
        .single()

      if (dbError) {
        throw new Error(`Database error: ${dbError.message}`)
      }

      return {
        supabaseFileId: fileRecord.id,
        storagePath: data.path
      }
    },
    onSuccess: (data, fileUpload) => {
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, status: 'completed', supabaseFileId: data.supabaseFileId }
          : f
      ))
      // Invalidate and refetch files list
      queryClient.invalidateQueries({ queryKey: ['files'] })
    },
    onError: (error: Error, fileUpload) => {
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, status: 'failed', error: error.message }
          : f
      ))
    }
  })

  const onDrop = useCallback((acceptedFiles: File[], rejectedFiles: any[]) => {
    setIsDragActive(false)

    // Handle rejected files
    if (rejectedFiles.length > 0) {
      const rejectedFile = rejectedFiles[0]
      const errors = rejectedFile.errors.map((e: any) => e.message).join(', ')
      setFiles(prev => [...prev, {
        id: Date.now().toString(),
        file: rejectedFile.file,
        progress: 0,
        status: 'failed',
        error: `File rejected: ${errors}`
      }])
      return
    }

    // Add accepted files to the list
    const newFiles: FileUpload[] = acceptedFiles.map(file => ({
      id: Date.now().toString() + Math.random(),
      file,
      progress: 0,
      status: 'pending'
    }))

    setFiles(prev => [...prev, ...newFiles])

    // Start uploading each file
    newFiles.forEach(fileUpload => {
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id ? { ...f, status: 'uploading' } : f
      ))
      uploadMutation.mutate(fileUpload)
    })
  }, [uploadMutation])

  const getAcceptedFileTypes = () => {
    switch (documentType) {
      case 'bank_statement':
        return BANK_STATEMENT_TYPES
      case 'ledger':
        return LEDGER_TYPES
      default:
        return ACCEPTED_FILE_TYPES
    }
  }

  const { getRootProps, getInputProps, isDragAccept, isDragReject } = useDropzone({
    onDrop,
    onDragEnter: () => setIsDragActive(true),
    onDragLeave: () => setIsDragActive(false),
    accept: getAcceptedFileTypes(),
    maxSize: MAX_FILE_SIZE,
    multiple: true
  })

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id))
  }

  const retryUpload = (fileUpload: FileUpload) => {
    setFiles(prev => prev.map(f =>
      f.id === fileUpload.id ? { ...f, status: 'uploading', error: undefined } : f
    ))
    uploadMutation.mutate(fileUpload)
  }

  const getFileIcon = (file: File) => {
    if (file.type.includes('pdf')) {
      return <FileText className="h-8 w-8 text-red-500" />
    }
    return <File className="h-8 w-8 text-green-500" />
  }

  const getStatusIcon = (status: FileUpload['status']) => {
    switch (status) {
      case 'uploading':
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />
      default:
        return null
    }
  }

  const getUploadText = () => {
    switch (documentType) {
      case 'bank_statement':
        return {
          title: 'Upload Bank Statements',
          description: 'Upload PDF bank statements for automatic transaction processing',
          formats: ['PDF']
        }
      case 'ledger':
        return {
          title: 'Upload Ledger Files',
          description: 'Upload Excel or CSV files containing your accounting ledger data',
          formats: ['XLSX', 'XLS', 'CSV']
        }
      default:
        return {
          title: 'Upload Financial Documents',
          description: 'Upload bank statements (PDF) and ledger files (Excel/CSV) for automatic reconciliation processing',
          formats: ['PDF', 'XLSX', 'XLS', 'CSV']
        }
    }
  }

  const uploadText = getUploadText()

  return (
    <div className="space-y-6">
      {/* Upload Zone */}
      <Card className="relative overflow-hidden">
        <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-500/5 to-blue-600/10 rounded-bl-full"></div>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Upload className="mr-2 h-5 w-5 text-blue-600" />
            {uploadText.title}
          </CardTitle>
          <CardDescription>
            {uploadText.description}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div
            {...getRootProps()}
            className={`
              border-2 border-dashed rounded-xl p-8 text-center cursor-pointer transition-all duration-200
              ${isDragAccept ? 'border-green-500 bg-green-50 scale-[1.02]' : ''}
              ${isDragReject ? 'border-red-500 bg-red-50 scale-[0.98]' : ''}
              ${!isDragActive ? 'border-gray-300 hover:border-blue-400 hover:bg-blue-50/30' : ''}
            `}
          >
            <input {...getInputProps()} />
            <div className="space-y-4">
              <div className="flex justify-center">
                <div className="p-4 bg-blue-100 rounded-full">
                  <Upload className="h-8 w-8 text-blue-600" />
                </div>
              </div>
              <div>
                <p className="text-xl font-semibold text-gray-900 mb-2">
                  {isDragActive ? 'Drop files here...' : 'Drag & drop files here'}
                </p>
                <p className="text-gray-600 mb-4">
                  or <span className="text-blue-600 font-medium">click to browse</span> your computer
                </p>
              </div>
              <div className="bg-gray-50 rounded-lg p-4 max-w-md mx-auto">
                <p className="text-sm text-gray-600 font-medium mb-2">Supported formats:</p>
                <div className="flex flex-wrap gap-2 justify-center">
                  {uploadText.formats.map((format) => (
                    <span key={format} className="px-2 py-1 bg-white border border-gray-200 rounded text-xs font-medium">
                      {format}
                    </span>
                  ))}
                </div>
                <p className="text-xs text-gray-500 mt-2">
                  Maximum file size: 10MB per file
                </p>
              </div>
            </div>
          </div>

          {/* File Type Guidelines */}
          <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="group p-4 bg-gradient-to-r from-blue-50 to-blue-100/70 rounded-lg border border-blue-200 hover:shadow-sm transition-shadow">
              <div className="flex items-start space-x-3">
                <div className="p-2 bg-blue-500 rounded-lg">
                  <FileText className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-blue-900">Bank Statements</h3>
                  <p className="text-sm text-blue-700 mt-1">
                    PDF files with transaction details, dates, and amounts
                  </p>
                  <ul className="text-xs text-blue-600 mt-2 space-y-1">
                    <li>• Monthly or quarterly statements</li>
                    <li>• Must include transaction descriptions</li>
                  </ul>
                </div>
              </div>
            </div>
            <div className="group p-4 bg-gradient-to-r from-green-50 to-green-100/70 rounded-lg border border-green-200 hover:shadow-sm transition-shadow">
              <div className="flex items-start space-x-3">
                <div className="p-2 bg-green-500 rounded-lg">
                  <File className="h-5 w-5 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-green-900">Ledger Files</h3>
                  <p className="text-sm text-green-700 mt-1">
                    Excel or CSV files with accounting entries
                  </p>
                  <ul className="text-xs text-green-600 mt-2 space-y-1">
                    <li>• Include date, amount, account columns</li>
                    <li>• First row should contain headers</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* File List */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5 text-gray-600" />
                  Uploaded Files ({files.length})
                </CardTitle>
                <CardDescription>
                  Track the progress of your file uploads and processing
                </CardDescription>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setFiles([])}
                className="text-gray-500 hover:text-gray-700"
              >
                Clear all
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {files.map((fileUpload) => (
                <div
                  key={fileUpload.id}
                  className="flex items-center space-x-4 p-4 bg-gray-50 hover:bg-gray-100 rounded-lg border border-gray-200 transition-colors"
                >
                  <div className="flex-shrink-0">
                    {getFileIcon(fileUpload.file)}
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <p className="font-medium text-gray-900 truncate">
                        {fileUpload.file.name}
                      </p>
                      <div className="flex items-center space-x-2 ml-4">
                        {getStatusIcon(fileUpload.status)}
                        <span className="text-xs font-medium text-gray-500 capitalize">
                          {fileUpload.status}
                        </span>
                      </div>
                    </div>
                    <p className="text-sm text-gray-500">
                      {(fileUpload.file.size / (1024 * 1024)).toFixed(2)} MB • {fileUpload.file.type}
                    </p>

                    {fileUpload.status === 'uploading' && (
                      <div className="mt-3">
                        <div className="flex justify-between text-xs text-gray-600 mb-1">
                          <span>Uploading...</span>
                          <span>{fileUpload.progress}%</span>
                        </div>
                        <Progress value={fileUpload.progress} className="h-2" />
                      </div>
                    )}

                    {fileUpload.error && (
                      <Alert className="mt-3 border-red-200 bg-red-50">
                        <AlertCircle className="h-4 w-4 text-red-600" />
                        <AlertDescription className="text-red-800">
                          {fileUpload.error}
                        </AlertDescription>
                      </Alert>
                    )}
                  </div>

                  <div className="flex items-center space-x-2">
                    {fileUpload.status === 'failed' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => retryUpload(fileUpload)}
                        className="text-red-600 border-red-200 hover:bg-red-50"
                      >
                        <Loader2 className="mr-1 h-3 w-3" />
                        Retry
                      </Button>
                    )}

                    {fileUpload.status === 'completed' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          // Future: Navigate to file details or processing status
                          console.log('View file details:', fileUpload.supabaseFileId)
                        }}
                        className="text-green-600 border-green-200 hover:bg-green-50"
                      >
                        View
                      </Button>
                    )}

                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(fileUpload.id)}
                      className="text-gray-400 hover:text-red-600"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}