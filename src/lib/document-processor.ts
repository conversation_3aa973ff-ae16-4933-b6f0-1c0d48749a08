import { Sandbox } from '@e2b/code-interpreter'

export interface ProcessingResult {
  success: boolean
  data?: any
  error?: string
  rawText?: string
  rowCount?: number
  columns?: string[]
}

export class DocumentProcessor {
  private sandbox: Sandbox | null = null

  async createSandbox(): Promise<void> {
    try {
      this.sandbox = await Sandbox.create()

      // Install required packages via code execution
      const installResult = await this.sandbox.runCode(`
import subprocess
import sys

# Install required packages
packages = ['PyMuPDF', 'pandas', 'openpyxl', 'requests', 'python-dateutil']
for package in packages:
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        print(f"Successfully installed {package}")
    except Exception as e:
        print(f"Failed to install {package}: {e}")

print("Package installation completed")
`)

      if (installResult.error) {
        console.error('Package installation error:', installResult.error)
      }

    } catch (error) {
      throw new Error(`Failed to create E2B sandbox: ${error}`)
    }
  }

  async processBankStatementPDF(
    fileBuffer: Buffer,
    fileName: string,
    mistralApiKey: string
  ): Promise<ProcessingResult> {
    if (!this.sandbox) {
      throw new Error('Sandbox not initialized')
    }

    try {
      // Upload PDF file to sandbox using Uint8Array
      const filePath = `/tmp/${fileName}`
      const uint8Array = new Uint8Array(fileBuffer)
      const blob = new Blob([uint8Array])
      await this.sandbox.files.write(filePath, blob)

      // Create and upload Python processing script
      const pythonScript = `
import os
import json
import requests
import fitz  # PyMuPDF

def extract_pdf_text(pdf_path):
    doc = fitz.open(pdf_path)
    text = ""
    for page in doc:
        text += page.get_text()
    doc.close()
    return text

def parse_with_mistral(text, api_key):
    prompt = f"""
Extract transaction data from this bank statement text and return valid JSON:
{{
    "bank_info": {{
        "bank_name": "string",
        "account_number": "string"
    }},
    "transactions": [
        {{
            "date": "YYYY-MM-DD",
            "description": "string",
            "reference": "string",
            "debit": null or number,
            "credit": null or number,
            "balance": number
        }}
    ]
}}

Bank statement text:
{text[:2000]}...
"""

    headers = {{
        'Authorization': f'Bearer {api_key}',
        'Content-Type': 'application/json'
    }}

    data = {{
        'model': 'mistral-large-latest',
        'messages': [{{ 'role': 'user', 'content': prompt }}],
        'max_tokens': 3000,
        'temperature': 0.1
    }}

    try:
        response = requests.post(
            'https://api.mistral.ai/v1/chat/completions',
            headers=headers,
            json=data,
            timeout=60
        )
        response.raise_for_status()
        result = response.json()
        content = result['choices'][0]['message']['content']

        # Extract JSON from response
        start = content.find('{{')
        end = content.rfind('}}') + 1
        if start >= 0 and end > start:
            json_str = content[start:end]
            return json.loads(json_str)
        else:
            return {{"error": "No valid JSON found in response"}}
    except Exception as e:
        return {{"error": f"API call failed: {str(e)}"}}

# Main processing
try:
    text = extract_pdf_text('${filePath}')
    if not text.strip():
        result = {{"success": False, "error": "No text extracted from PDF"}}
    else:
        parsed_data = parse_with_mistral(text, '${mistralApiKey}')
        if "error" in parsed_data:
            result = {{"success": False, "error": parsed_data["error"]}}
        else:
            result = {{"success": True, "data": parsed_data, "raw_text": text[:500]}}
except Exception as e:
    result = {{"success": False, "error": str(e)}}

print(json.dumps(result))
`

      await this.sandbox.files.write('/tmp/process_pdf.py', pythonScript)

      // Run the processing script
      const result = await this.sandbox.runCode('exec(open("/tmp/process_pdf.py").read())')

      if (result.error) {
        throw new Error(`Processing failed: ${result.error}`)
      }

      const output = result.text || '{}'
      return JSON.parse(output) as ProcessingResult

    } catch (error) {
      return {
        success: false,
        error: `PDF processing failed: ${error}`
      }
    }
  }

  async processLedgerFile(
    fileBuffer: Buffer,
    fileName: string,
    geminiApiKey: string
  ): Promise<ProcessingResult> {
    if (!this.sandbox) {
      throw new Error('Sandbox not initialized')
    }

    try {
      // Upload file to sandbox using Uint8Array
      const filePath = `/tmp/${fileName}`
      const uint8Array = new Uint8Array(fileBuffer)
      const blob = new Blob([uint8Array])
      await this.sandbox.files.write(filePath, blob)

      // Create and upload Python processing script
      const pythonScript = `
import os
import json
import requests
import pandas as pd

def read_file(file_path):
    try:
        if file_path.endswith('.csv'):
            return pd.read_csv(file_path)
        else:
            return pd.read_excel(file_path)
    except Exception as e:
        raise Exception(f"Failed to read file: {str(e)}")

def parse_with_gemini(df, api_key):
    df_preview = df.head(20).to_string()
    columns = list(df.columns)

    prompt = f"""
Analyze this ledger data and return valid JSON:
{{
    "ledger_info": {{
        "organization": "string",
        "period_start": "YYYY-MM-DD",
        "period_end": "YYYY-MM-DD"
    }},
    "transactions": [
        {{
            "date": "YYYY-MM-DD",
            "description": "string",
            "reference": "string",
            "debit": null or number,
            "credit": null or number,
            "balance": number
        }}
    ]
}}

Columns: {columns}
Data preview:
{df_preview}
"""

    headers = {{'Content-Type': 'application/json'}}
    data = {{
        'contents': [{{'parts': [{{'text': prompt}}]}}],
        'generationConfig': {{'temperature': 0.1, 'maxOutputTokens': 3000}}
    }}

    try:
        url = f'https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash-latest:generateContent?key={api_key}'
        response = requests.post(url, headers=headers, json=data, timeout=60)
        response.raise_for_status()
        result = response.json()
        content = result['candidates'][0]['content']['parts'][0]['text']

        # Extract JSON from response
        start = content.find('{{')
        end = content.rfind('}}') + 1
        if start >= 0 and end > start:
            json_str = content[start:end]
            return json.loads(json_str)
        else:
            return {{"error": "No valid JSON found in response"}}
    except Exception as e:
        return {{"error": f"API call failed: {str(e)}"}}

# Main processing
try:
    df = read_file('${filePath}')
    if df.empty:
        result = {{"success": False, "error": "No data found in file"}}
    else:
        parsed_data = parse_with_gemini(df, '${geminiApiKey}')
        if "error" in parsed_data:
            result = {{"success": False, "error": parsed_data["error"]}}
        else:
            result = {{"success": True, "data": parsed_data, "row_count": len(df), "columns": list(df.columns)}}
except Exception as e:
    result = {{"success": False, "error": str(e)}}

print(json.dumps(result))
`

      await this.sandbox.files.write('/tmp/process_excel.py', pythonScript)

      // Run the processing script
      const result = await this.sandbox.runCode('exec(open("/tmp/process_excel.py").read())')

      if (result.error) {
        throw new Error(`Processing failed: ${result.error}`)
      }

      const output = result.text || '{}'
      return JSON.parse(output) as ProcessingResult

    } catch (error) {
      return {
        success: false,
        error: `Ledger processing failed: ${error}`
      }
    }
  }

  async cleanup(): Promise<void> {
    if (this.sandbox) {
      try {
        await this.sandbox.kill()
      } catch (error) {
        console.error('Error closing sandbox:', error)
      }
      this.sandbox = null
    }
  }
}