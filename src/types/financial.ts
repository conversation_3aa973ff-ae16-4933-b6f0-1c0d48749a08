// Financial data types
export interface Transaction {
  id: string
  date: string
  description: string
  amount: number
  balance?: number | null
  reference?: string | null
  account?: string | null
  type: 'bank' | 'ledger'
  fileId: string
  userId: string
  companyId: string
  createdAt: string
  updatedAt: string
}

export interface BankTransaction extends Transaction {
  type: 'bank'
  balance: number
}

export interface LedgerTransaction extends Transaction {
  type: 'ledger'
  debit?: number
  credit?: number
  account: string
}

export interface ReconciliationMatch {
  id: string
  bankTransaction?: BankTransaction | null
  ledgerTransaction?: LedgerTransaction | null
  status: 'matched' | 'unmatched' | 'disputed'
  confidenceScore: number
  notes?: string | null
}

export interface ProcessingResult {
  success: boolean
  fileId: string
  transactionCount: number
  errors?: string[]
  processingTime: number
}

export interface FileUpload {
  id: string
  filename: string
  fileType: 'pdf' | 'excel' | 'csv'
  fileSize: number
  status: 'uploaded' | 'processing' | 'completed' | 'failed'
  storagePath: string
  createdAt: string
}

export interface ReconciliationSummary {
  totalTransactions: number
  matchedTransactions: number
  unmatchedBankTransactions: number
  unmatchedLedgerTransactions: number
  matchPercentage: number
  balanceDifference: number
}

export interface DiscrepancyReport {
  summary: ReconciliationSummary
  unmatchedBank: BankTransaction[]
  unmatchedLedger: LedgerTransaction[]
  disputes: ReconciliationMatch[]
  recommendations: JournalVoucherRecommendation[]
}

export interface JournalVoucherRecommendation {
  type: 'adjustment' | 'correction' | 'investigation'
  description: string
  amount: number
  account?: string
  priority: 'high' | 'medium' | 'low'
}