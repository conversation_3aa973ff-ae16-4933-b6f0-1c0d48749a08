-- CORRECTED: Supabase Storage Setup
-- This fixes the permission error when creating storage policies

-- Step 1: Create the bucket (can also be done via Dashboard)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'financial-documents',
  'financial-documents',
  false,
  10485760, -- 10MB limit
  ARRAY[
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'text/csv'
  ]
);

-- Step 2: Create simplified storage policies
-- Note: If you get permission errors, create these through the Supabase Dashboard instead

-- Policy 1: Allow authenticated users to upload files
CREATE POLICY "Allow authenticated uploads" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'financial-documents' AND
    auth.role() = 'authenticated'
  );

-- Policy 2: Allow users to view files in their folder structure
CREATE POLICY "Allow users to view files" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'financial-documents' AND
    auth.role() = 'authenticated'
  );

-- Policy 3: Allow users to delete files (optional)
CREATE POLICY "Allow users to delete files" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'financial-documents' AND
    auth.role() = 'authenticated'
  );

-- Alternative: If the above still gives permission errors,
-- create the bucket via Dashboard and use these simpler policies:

/*
-- Ultra-simple policies (create through Dashboard > Storage > Policies)
-- Policy Name: "Enable uploads for authenticated users"
-- Operation: INSERT
-- Check: bucket_id = 'financial-documents' AND auth.role() = 'authenticated'

-- Policy Name: "Enable downloads for authenticated users"
-- Operation: SELECT
-- Using: bucket_id = 'financial-documents' AND auth.role() = 'authenticated'
*/