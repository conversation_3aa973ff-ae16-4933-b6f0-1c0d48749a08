-- Create Supabase Storage bucket for financial documents
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'financial-documents',
  'financial-documents',
  false,
  10485760, -- 10MB limit
  ARRAY[
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'application/vnd.ms-excel',
    'text/csv'
  ]
);

-- Storage policy: Users can upload files to their company's folder
CREATE POLICY "Company users can upload files" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'financial-documents' AND
    (storage.foldername(name))[1] = 'uploads' AND
    auth.uid() IN (
      SELECT user_id FROM company_users
      WHERE company_id::text = (storage.foldername(name))[2]
    )
  );

-- Storage policy: Users can view files from their companies
CREATE POLICY "Company users can view files" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'financial-documents' AND
    auth.uid() IN (
      SELECT user_id FROM company_users
      WHERE company_id::text = (storage.foldername(name))[2]
    )
  );

-- Storage policy: Users can delete files from their companies (admin only)
CREATE POLICY "Company admins can delete files" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'financial-documents' AND
    auth.uid() IN (
      SELECT user_id FROM company_users
      WHERE company_id::text = (storage.foldername(name))[2]
      AND role = 'admin'
    )
  );

-- Enable RLS on storage.objects
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;