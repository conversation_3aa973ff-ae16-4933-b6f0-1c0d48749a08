-- Seed data for development
-- This file contains sample data to help with development and testing

-- Insert a sample company
INSERT INTO companies (id, name, slug, settings) VALUES
(
    '********-0000-0000-0000-************',
    'RFSA Demo Company',
    'rfsa-demo',
    '{
        "currency": "USD",
        "fiscal_year_start": "01-01",
        "timezone": "America/New_York",
        "reconciliation_settings": {
            "auto_match_threshold": 95.0,
            "date_tolerance_days": 3,
            "amount_tolerance_cents": 0
        }
    }'::jsonb
);

-- Note: User profiles will be created automatically via trigger when users sign up
-- Company users will be created when users join companies through the application

-- You can run this seed data after creating your first user account through the application
-- Replace 'YOUR_USER_ID' with the actual user ID from auth.users after signup

/*
-- Example: Add your user to the demo company (run after user signup)
INSERT INTO company_users (company_id, user_id, role, accepted_at) VALUES
(
    '********-0000-0000-0000-************',
    'YOUR_USER_ID',
    'admin',
    NOW()
);
*/