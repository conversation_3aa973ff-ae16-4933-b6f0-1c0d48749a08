#!/usr/bin/env node

/**
 * Test script to verify the database fix worked
 */

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function testDatabaseFix() {
  console.log('🧪 Testing database fix...\n')
  
  const tablesToTest = [
    'accounting_companies',
    'user_profiles',
    'company_users', 
    'files',
    'transactions'
  ]
  
  let allTablesExist = true
  
  for (const tableName of tablesToTest) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
      
      if (error) {
        console.log(`❌ Table '${tableName}': ${error.message}`)
        allTablesExist = false
      } else {
        console.log(`✅ Table '${tableName}': OK`)
      }
    } catch (err) {
      console.log(`❌ Table '${tableName}': ${err.message}`)
      allTablesExist = false
    }
  }
  
  console.log('\n' + '='.repeat(50))
  
  if (allTablesExist) {
    console.log('🎉 SUCCESS! All required tables exist.')
    console.log('✅ Your accounting app should now work without errors.')
    console.log('\nNext steps:')
    console.log('1. Restart your development server: npm run dev')
    console.log('2. Try uploading a document')
    console.log('3. Check that no 500 errors occur')
  } else {
    console.log('❌ FAILED! Some tables are missing.')
    console.log('\nTroubleshooting:')
    console.log('1. Make sure you ran minimal-database-fix.sql in Supabase SQL Editor')
    console.log('2. Check for any error messages in the SQL Editor output')
    console.log('3. Verify your service role key has admin permissions')
  }
}

testDatabaseFix().catch(console.error)
