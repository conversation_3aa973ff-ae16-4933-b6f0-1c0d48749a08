// Test script to verify the ensure-company API endpoint
// This can be run to test the company creation logic

import { createClient } from '@supabase/supabase-js'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function testEnsureCompany() {
  try {
    // Get the current session
    const { data: { session } } = await supabase.auth.getSession()

    if (!session) {
      console.log('No active session - user needs to be logged in')
      return
    }

    console.log('Testing ensure-company API...')

    // Call the API endpoint
    const response = await fetch('/api/ensure-company', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
      },
    })

    if (!response.ok) {
      const error = await response.json()
      console.error('API Error:', error)
      return
    }

    const { companyId } = await response.json()
    console.log('Success! Company ID:', companyId)

    // Verify the company exists in the database
    const { data: company } = await supabase
      .from('companies')
      .select('*')
      .eq('id', companyId)
      .single()

    console.log('Company details:', company)

  } catch (error) {
    console.error('Test failed:', error)
  }
}

// Export for manual testing
export { testEnsureCompany }