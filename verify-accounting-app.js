#!/usr/bin/env node

/**
 * Complete verification script for accounting app setup
 */

const { createClient } = require('@supabase/supabase-js')

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing environment variables')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseServiceKey)

async function verifySetup() {
  console.log('🔍 Verifying Accounting App Setup...\n')
  
  let allGood = true
  
  // Test 1: Database Tables
  console.log('📊 Testing Database Tables:')
  const requiredTables = [
    'accounting_companies',
    'user_profiles',
    'company_users',
    'files',
    'transactions'
  ]
  
  for (const tableName of requiredTables) {
    try {
      const { data, error } = await supabase
        .from(tableName)
        .select('*')
        .limit(1)
      
      if (error) {
        console.log(`   ❌ ${tableName}: ${error.message}`)
        allGood = false
      } else {
        console.log(`   ✅ ${tableName}: OK`)
      }
    } catch (err) {
      console.log(`   ❌ ${tableName}: ${err.message}`)
      allGood = false
    }
  }
  
  // Test 2: Storage Buckets
  console.log('\n📁 Testing Storage Buckets:')
  try {
    const { data: buckets, error: listError } = await supabase.storage.listBuckets()
    
    if (listError) {
      console.log(`   ❌ Storage access error: ${listError.message}`)
      allGood = false
    } else {
      const hasFinancialDocs = buckets.some(b => b.name === 'financial-documents')
      if (hasFinancialDocs) {
        console.log('   ✅ financial-documents bucket: OK')
      } else {
        console.log('   ❌ financial-documents bucket: Missing')
        allGood = false
      }
    }
  } catch (err) {
    console.log(`   ❌ Storage error: ${err.message}`)
    allGood = false
  }
  
  // Test 3: API Endpoints
  console.log('\n🔌 Testing API Endpoints:')
  try {
    // Test ensure-company endpoint
    const response = await fetch(`${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/api/ensure-company`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
    })
    
    if (response.status === 401) {
      console.log('   ✅ /api/ensure-company: OK (returns 401 without auth - expected)')
    } else if (response.status === 500) {
      const error = await response.json()
      if (error.error?.includes('No authorization header')) {
        console.log('   ✅ /api/ensure-company: OK (auth required - expected)')
      } else {
        console.log(`   ❌ /api/ensure-company: ${error.error}`)
        allGood = false
      }
    } else {
      console.log(`   ⚠️  /api/ensure-company: Unexpected status ${response.status}`)
    }
  } catch (err) {
    console.log(`   ❌ API test failed: ${err.message}`)
    allGood = false
  }
  
  // Test 4: Environment Variables
  console.log('\n🔧 Testing Environment Variables:')
  const requiredEnvVars = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'SUPABASE_SERVICE_ROLE_KEY',
    'GOOGLE_GENERATIVE_AI_API_KEY',
    'MISTRAL_API_KEY',
    'E2B_API_KEY'
  ]
  
  for (const envVar of requiredEnvVars) {
    if (process.env[envVar]) {
      console.log(`   ✅ ${envVar}: Set`)
    } else {
      console.log(`   ❌ ${envVar}: Missing`)
      if (envVar.includes('SUPABASE')) {
        allGood = false // Critical for basic functionality
      }
    }
  }
  
  // Final Result
  console.log('\n' + '='.repeat(60))
  
  if (allGood) {
    console.log('🎉 SUCCESS! Your accounting app is ready to use!')
    console.log('\n✅ Next steps:')
    console.log('   1. Make sure your dev server is running: npm run dev')
    console.log('   2. Go to http://localhost:3000')
    console.log('   3. Sign up/login with Supabase Auth')
    console.log('   4. Try uploading a bank statement (PDF) or ledger (Excel/CSV)')
    console.log('   5. Check that files upload without errors')
    console.log('\n🚀 Your accounting app features:')
    console.log('   • Automated bank reconciliation')
    console.log('   • AI-powered transaction extraction')
    console.log('   • Discrepancy detection and reporting')
    console.log('   • Multi-tenant company support')
  } else {
    console.log('❌ SETUP INCOMPLETE!')
    console.log('\n🔧 Issues found that need fixing:')
    console.log('   • Check the errors above')
    console.log('   • Make sure you ran minimal-database-fix.sql in Supabase')
    console.log('   • Verify your environment variables in .env.local')
    console.log('   • Ensure your Supabase service role key has admin permissions')
  }
}

verifySetup().catch(console.error)
